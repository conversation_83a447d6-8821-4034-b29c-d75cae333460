import { defineConfig } from 'vite'
import { resolve } from 'path'
import pkg from './package.json'

export default defineConfig({
  build: {
    emptyOutDir: true,
    rollupOptions: {
      input: resolve(__dirname, 'packages/life-jssdk.js'),
      output: {
        dir: 'dist',
        entryFileNames: `fxjs-${pkg.version}.js`,
        // format: 'umd', 自行实现了模块定义，在浏览器环境中兼容了 AMD CMD 和 全局变量的引入方式
        globals: {
          window: 'window',
        },
      },
    },
    minify: 'terser',
    terserOptions: {
      mangle: {
        properties: {
          // 保留这些方法/属性名称不混淆
          reserved: [
            'init',
            'callBackToH5',
            'success',
            'fail',
            'complete',
            'webkit',
            'messageHandlers',
            'nativeInvoke',
            'data',
            'code',
            'msg',
            'type',
            'postMessage',
            'isDebug',
            'bizCode',
            'biz_code',
          ],
        },
      },
      compress: {
        passes: 3, // 压缩次数
        drop_debugger: true,
        // pure_funcs: ['console.log'],
      },
      format: {
        comments: false,
      },
    },
  },
})
