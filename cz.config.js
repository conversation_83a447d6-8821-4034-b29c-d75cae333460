// import { defineConfig } from 'cz-git'

export default {
  /*  alias: {
    wip: '🚧 开发中',
    feat: '✨ 新功能',
    fix: '🐛 修复bug',
    docs: '📝 文档变更',
    style: '💄 代码格式(不影响代码运行的变动)',
    refactor: '♻️ 重构(既不是增加feature，也不是修复bug)',
  }, */
  messages: {
    type: '选择你要提交的类型 :',
    scope: '选择一个提交范围（可选）:',
    customScope: '请输入自定义的提交范围 :',
    subject: '请简要描述提交(必填):\n',
    body: '填写更加详细的变更描述（可选）。使用 "|" 换行 :\n',
    markBreaking: '是否重大变更 ?\n',
    breaking: '列举非兼容性重大的变更（可选）。使用 "|" 换行 :\n',
    footerPrefixesSelect: '选择关联issue前缀（可选）:',
    customFooterPrefix: '输入自定义issue前缀 :',
    footer: '请输入需求/任务地址 :\n',
    confirmCommit: '确认使用以上信息提交？(y/n/e/h)',
  },
  types: [
    { value: 'wip', name: 'wip:     🚧 开发中' },
    { value: 'feat', name: 'feat:     ✨ 新功能' },
    { value: 'fix', name: 'fix:      🐛 修复bug' },
    { value: 'docs', name: 'docs:     📝 文档变更' },
    { value: 'style', name: 'style:    💄 代码格式(不影响代码运行的变动)' },
    { value: 'refactor', name: 'refactor: ♻️  重构(既不是增加feature，也不是修复bug)' },
    { value: 'perf', name: 'perf:     ⚡️ 性能优化' },
    { value: 'test', name: 'test:     ✅ 增加测试' },
    { value: 'chore', name: 'chore:    🔨 构建过程或辅助工具的变动' },
    { value: 'revert', name: 'revert:   ⏪️ 回退' },
    { value: 'build', name: 'build:    📦️ 打包' },
  ],
  useEmoji: false,
  emojiAlign: 'center',
  useAI: false,
  aiNumber: 1,
  themeColorCode: '',
  scopes: [
    { value: 'demo', name: '调试demo' },
    { value: 'life-jssdk', name: '生活jssdk' },
    { value: 'capacity-limit', name: '页面资源收集+js能力限制' },
    { value: 'launch-wechat', name: '微信小程序scheme' },
  ],
  allowCustomScopes: false,
  allowEmptyScopes: true,
  customScopesAlign: 'bottom',
  customScopesAlias: 'custom',
  emptyScopesAlias: 'empty',
  upperCaseSubject: false,
  markBreakingChangeMode: false,
  allowBreakingChanges: ['feat', 'fix'],
  breaklineNumber: 100,
  breaklineChar: '|',
  skipQuestions: ['body', 'footer'],
  issuePrefixes: [{ value: '需求/任务地址: ', name: '需求/任务地址' }],
  customIssuePrefixAlign: 'top',
  emptyIssuePrefixAlias: '跳过',
  customIssuePrefixAlias: '自定义',
  allowCustomIssuePrefix: false,
  allowEmptyIssuePrefix: false,
  confirmColorize: true,
  maxHeaderLength: Infinity,
  maxSubjectLength: Infinity,
  minSubjectLength: 0,
  scopeOverrides: undefined,
  defaultBody: '',
  defaultIssues: '',
  defaultScope: '',
  defaultSubject: '',
}
