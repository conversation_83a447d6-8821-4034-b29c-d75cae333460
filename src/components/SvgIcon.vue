<template>
  <svg :class="svgClass" :style="svgStyle" aria-hidden="true" v-bind="$attrs">
    <use :href="symbolId" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  /** 图标名称 */
  name: string
  /** 图标颜色 */
  color?: string
  /** 图标大小 */
  size?: string | number
  /** 自定义类名 */
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  color: 'currentColor',
  size: '1em',
  className: '',
})

// 计算 symbol ID (简化版，不需要 prefix)
const symbolId = computed(() => `#icon-${props.name}`)

// 计算 SVG 类名
const svgClass = computed(() => ['svg-icon', props.className])

// 计算 SVG 样式
const svgStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
}))
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
}
</style>
