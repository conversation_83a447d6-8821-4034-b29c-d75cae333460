<!--
 * @Author: ho<PERSON><PERSON><PERSON>
 * @Date: 2024-12-17 09:16:01
 * @Description: 现代化设计的开发调试平台首页
 * @LastEditTime: 2025-04-29 11:22:11
 * @LastEditors: houbaoguo
-->
<template>
  <div class="home-page">
    <div class="page-container">
      <!-- 现代化功能网格 -->
      <div class="features-section">
        <h2 class="section-title">功能模块</h2>
        <div class="features-grid">
          <!-- 调试资源收集 -->
          <router-link to="/dev/test-one" class="feature-card">
            <div class="card-icon primary">
              <SvgIcon name="clipboard" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">调试资源收集</h3>
              <p class="card-desc">收集调试所需的资源</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- 调试协议 -->
          <router-link to="/sdk/protocol" class="feature-card">
            <div class="card-icon success">
              <SvgIcon name="document" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">调试协议</h3>
              <p class="card-desc">协议接口调试</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- Web能力限制 -->
          <router-link to="/features/web-capacity" class="feature-card">
            <div class="card-icon purple">
              <SvgIcon name="shield" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">Web能力限制</h3>
              <p class="card-desc">检测API访问限制</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- SDK方法测试 -->
          <router-link to="/sdk/methods" class="feature-card">
            <div class="card-icon warning">
              <SvgIcon name="code" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">SDK方法测试</h3>
              <p class="card-desc">测试SDK接口方法</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- MEP桥接 -->
          <router-link to="/integrations/mep-bridge" class="feature-card">
            <div class="card-icon info">
              <SvgIcon name="lightning" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">MEP桥接</h3>
              <p class="card-desc">MEP桥接功能测试</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- 页面跳转 -->
          <router-link to="/features/page-jump" class="feature-card">
            <div class="card-icon primary">
              <SvgIcon name="external-link" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">页面跳转</h3>
              <p class="card-desc">页面跳转功能测试</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- 微信小程序 -->
          <router-link to="/integrations/wechat-miniprogram" class="feature-card">
            <div class="card-icon success">
              <SvgIcon name="wechat" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">微信小程序</h3>
              <p class="card-desc">启动微信小程序功能</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>

          <!-- 开发测试页面 2 -->
          <router-link to="/dev/test-two" class="feature-card">
            <div class="card-icon warning">
              <SvgIcon name="settings" size="24" />
            </div>
            <div class="card-content">
              <h3 class="card-title">开发测试页面 2</h3>
              <p class="card-desc">开发和调试用的测试页面</p>
            </div>
            <div class="card-arrow">
              <SvgIcon name="chevron-right" size="16" />
            </div>
          </router-link>
        </div>
      </div>

      <!-- 收银台测试 -->
      <div class="payment-section">
        <div class="payment-card">
          <div class="payment-header">
            <div class="payment-icon">
              <SvgIcon name="credit-card" size="28" />
            </div>
            <div class="payment-info">
              <h3 class="payment-title">收银台测试</h3>
              <p class="payment-desc">测试支付功能</p>
            </div>
          </div>
          <div class="payment-form">
            <div class="form-group">
              <label class="form-label">订单号</label>
              <input v-model="orderNo" type="text" placeholder="请输入订单号" class="form-input" />
            </div>
            <button @click="test" class="payment-button">
              <SvgIcon name="credit-card" size="18" />
              启动收银台
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const orderNo = ref('')

const test = () => {
  if (!orderNo.value.trim()) {
    alert('请输入订单号')
    return
  }

  console.log('启动收银台，订单号：', orderNo.value)
  // 这里可以添加实际的收银台启动逻辑
}
</script>

<style lang="scss" scoped>
.home-page {
  @include modern-page-container();
}

.page-container {
  @include content-container();
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 功能区域
.features-section {
  margin-bottom: space(12);

  .section-title {
    font-size: font(h2);
    font-weight: weight(semibold);
    color: color(text-primary);
    text-align: center;
    margin-bottom: space(8);
  }
}

// 功能网格
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: space(6);
}

// Apple 风格功能卡片
.feature-card {
  @include card();
  display: flex;
  align-items: center;
  gap: space(4);
  text-decoration: none;
  padding: space(4);
  transition: all 0.25s ease-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }

  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: radius(xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;

    svg {
      color: white;
      z-index: 2;
      position: relative;
    }

    // Apple 风格的协调背景色
    &.primary {
      background: linear-gradient(135deg, color(primary) 0%, color(primary-600) 100%);
    }
    &.success {
      background: linear-gradient(135deg, color(success) 0%, color(success-600) 100%);
    }
    &.warning {
      background: linear-gradient(135deg, color(warning) 0%, color(warning-600) 100%);
    }
    &.error {
      background: linear-gradient(135deg, color(error) 0%, color(error-600) 100%);
    }
    &.purple {
      background: linear-gradient(135deg, color(purple) 0%, color(purple-600) 100%);
    }
    &.info {
      background: linear-gradient(135deg, color(info) 0%, color(info-600) 100%);
    }

    // 添加微妙的光泽效果
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: inherit;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
      z-index: 1;
    }
  }

  .card-content {
    flex: 1;

    .card-title {
      font-size: font(headline);
      font-weight: weight(semibold);
      color: color(text-primary);
      margin-bottom: space(1);
    }

    .card-desc {
      font-size: font(subheadline);
      color: color(text-secondary);
      line-height: 1.4;
    }
  }

  .card-arrow {
    color: color(text-tertiary);
    transition: transform 0.2s ease;
  }

  &:hover .card-arrow {
    transform: translateX(4px);
  }
}

// 收银台区域
.payment-section {
  .payment-card {
    @include glass-card();
    max-width: 500px;
    margin: 0 auto;

    .payment-header {
      display: flex;
      align-items: center;
      gap: space(4);
      margin-bottom: space(6);
      padding-bottom: space(4);
      border-bottom: 1px solid color(border-light);

      .payment-icon {
        width: 56px;
        height: 56px;
        background: gradient(primary);
        border-radius: radius(lg);
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          color: white;
        }
      }

      .payment-info {
        .payment-title {
          font-size: font(h3);
          font-weight: weight(semibold);
          color: color(text-primary);
          margin-bottom: space(1);
        }

        .payment-desc {
          font-size: font(body-sm);
          color: color(text-secondary);
        }
      }
    }

    .payment-form {
      .form-group {
        margin-bottom: space(4);

        .form-label {
          display: block;
          font-size: font(body-sm);
          font-weight: weight(medium);
          color: color(text-primary);
          margin-bottom: space(2);
        }

        .form-input {
          @include input();
        }
      }

      .payment-button {
        @include button-primary();
        width: 100%;
        gap: space(2);
      }
    }
  }
}

// 响应式设计
@include responsive(md) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: space(4);
  }
}
</style>
