<template>
  <div class="test1">
    <router-link to="/dev/test-two">
      <button
        class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-2"
      >
        <SvgIcon name="external-link" size="18" class="text-white" />
        跳转测试页面2
      </button>
    </router-link>

    <p>这是测试页面1的内容</p>

    <img src="@/assets/images/1.jpeg" alt="测试图片1" class="test-image" />
    <img
      src="data:image/jpeg;base64,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"
    />
  </div>
</template>

<style scoped>
.test1 {
  padding: 20px;
}

.test-image {
  max-width: 300px;
  margin: 20px 0;
}
</style>
