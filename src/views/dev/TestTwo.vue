<template>
  <div class="test-two-page">
    <div class="container mx-auto px-4 py-6">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">测试页面 2</h1>
        <p class="text-gray-600">开发测试功能页面</p>
        <div
          class="mt-4 inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium"
        >
          <SvgIcon name="code" size="16" class="mr-2" />
          开发测试
        </div>
      </div>

      <!-- 内容卡片 -->
      <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          <div
            class="px-6 py-5 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-lg font-semibold text-gray-900">测试内容</h2>
                <p class="text-sm text-gray-500 mt-1">这是一个开发测试页面</p>
              </div>
              <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <SvgIcon name="code" size="24" class="text-green-600" />
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-6">
              <!-- 文本内容 -->
              <div class="text-content">
                <h3 class="text-base font-semibold text-gray-900 mb-3">页面说明</h3>
                <p class="text-gray-600 leading-relaxed">
                  这是测试页面2的内容，用于开发和测试各种功能。 页面采用现代化的设计风格，使用 SCSS
                  进行样式管理。
                </p>
              </div>

              <!-- 图片展示 -->
              <div class="image-section">
                <h3 class="text-base font-semibold text-gray-900 mb-3">测试图片</h3>
                <div class="image-container">
                  <img
                    src="https://picsum.photos/id/10/400/300"
                    alt="测试图片2"
                    class="test-image"
                  />
                  <p class="image-caption">随机测试图片 - 400x300</p>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="actions">
                <button class="action-btn">
                  <SvgIcon name="play" size="18" class="mr-2" />
                  执行测试
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.test-two-page {
  width: 100%;
  background: color(bg-secondary);
  font-family:
    -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: color(text-primary);
  line-height: 1.47;

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: space(4) space(4) space(6);
  }

  // 页面标题
  .page-title {
    text-align: center;
    margin-bottom: space(8);

    .badge {
      background: color(success-100);
      color: color(success-600);
    }
  }

  // 主要内容区域
  .main-content {
    max-width: 48rem;
    margin: 0 auto;

    .main-card {
      @include card();
      border: 1px solid color(border-light);
      overflow: hidden;

      .card-header {
        padding: space(6) space(6) space(5);
        background: linear-gradient(to right, #f0fdf4, #ecfdf5);
        border-bottom: 1px solid color(border-light);

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .header-text {
            h2 {
              font-size: font(h3);
              font-weight: weight(semibold);
              color: color(text-primary);
            }

            p {
              font-size: font(body-sm);
              color: color(text-secondary);
              margin-top: space(1);
            }
          }

          .header-icon {
            width: space(12);
            height: space(12);
            background: color(success-100);
            border-radius: radius(xl);
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .card-body {
        padding: space(6);

        .space-y-6 > * + * {
          margin-top: space(6);
        }
      }
    }
  }

  // 文本内容
  .text-content {
    h3 {
      font-size: font(body);
      font-weight: weight(semibold);
      color: color(text-primary);
      margin-bottom: space(3);
    }

    p {
      color: color(text-secondary);
      line-height: 1.6;
    }
  }

  // 图片区域
  .image-section {
    h3 {
      font-size: font(body);
      font-weight: weight(semibold);
      color: color(text-primary);
      margin-bottom: space(3);
    }

    .image-container {
      text-align: center;

      .test-image {
        max-width: 100%;
        height: auto;
        border-radius: radius(lg);
        box-shadow: shadow(md);
        transition: transition(normal);

        &:hover {
          box-shadow: shadow(lg);
          transform: scale(1.02);
        }
      }

      .image-caption {
        margin-top: space(3);
        font-size: font(body-sm);
        color: color(text-tertiary);
        font-style: italic;
      }
    }
  }

  // 操作按钮
  .actions {
    display: flex;
    justify-content: center;

    .action-btn {
      @include button-primary();
      background: gradient(success);
      gap: space(2);
    }
  }

  // 响应式优化
  @include responsive(md) {
    padding: 0;

    .test-image {
      max-width: 100%;
    }
  }
}
</style>
