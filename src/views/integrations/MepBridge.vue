<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-04-01 17:21:06
 * @Description: MEP桥接功能测试页面
 * @LastEditTime: 2025-04-01 17:27:41
 * @LastEditors: houbaoguo
-->
<template>
  <div class="mep-bridge-page">
    <div class="page-container">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">MEP桥接测试</h1>
        <p class="text-gray-600">测试移动端原生桥接功能</p>
        <div
          class="mt-4 inline-flex items-center px-4 py-2 rounded-full bg-indigo-100 text-indigo-800 text-sm font-medium"
        >
          <SvgIcon name="lightning" size="16" class="mr-2" />
          原生桥接
        </div>
      </div>

      <!-- 功能测试卡片 -->
      <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          <div
            class="px-6 py-5 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-lg font-semibold text-gray-900">扫码功能测试</h2>
                <p class="text-sm text-gray-500 mt-1">测试iOS和Android平台的扫码回调</p>
              </div>
              <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                <SvgIcon name="lightning" size="24" class="text-indigo-600" />
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-6">
              <!-- iOS 扫码测试 -->
              <div class="test-section">
                <div class="test-header">
                  <div class="test-icon ios">
                    <SvgIcon name="shield" size="20" class="text-blue-600" />
                  </div>
                  <div class="test-info">
                    <h3 class="test-title">iOS 扫码测试</h3>
                    <p class="test-desc">通过 webkit.messageHandlers 调用原生扫码功能</p>
                  </div>
                </div>
                <button @click="iosScanTheCode" class="test-btn ios-btn">
                  <SvgIcon name="play" size="16" class="mr-2" />
                  iOS 扫码回调
                </button>
              </div>

              <!-- Android 扫码测试 -->
              <div class="test-section">
                <div class="test-header">
                  <div class="test-icon android">
                    <SvgIcon name="code" size="20" class="text-green-600" />
                  </div>
                  <div class="test-info">
                    <h3 class="test-title">Android 扫码测试</h3>
                    <p class="test-desc">通过 szkd_android 接口调用原生扫码功能</p>
                  </div>
                </div>
                <button @click="androidScanTheCode" class="test-btn android-btn">
                  <SvgIcon name="play" size="16" class="mr-2" />
                  Android 扫码回调
                </button>
              </div>

              <!-- 回调说明 -->
              <div class="callback-info">
                <h3 class="text-base font-semibold text-gray-900 mb-3">回调说明</h3>
                <div class="info-box">
                  <SvgIcon
                    name="document"
                    size="16"
                    class="text-blue-500 mr-2 flex-shrink-0 mt-0.5"
                  />
                  <p class="text-sm text-gray-600 leading-relaxed">
                    扫码结果将通过 <code class="code-inline">getOtherScanTheCodeCallBack</code>
                    回调函数返回，请查看控制台输出。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 添加类型声明
declare global {
  interface Window {
    webkit?: {
      messageHandlers?: {
        getOtherScanTheCode?: {
          postMessage: (data?: string) => void
        }
      }
    }
    szkd_android?: {
      getOtherScanTheCode: () => void
    }
    getOtherScanTheCodeCallBack: (res: unknown) => void
  }
}

const iosScanTheCode = () => {
  window?.webkit?.messageHandlers?.getOtherScanTheCode?.postMessage()
}
const androidScanTheCode = () => {
  window?.szkd_android?.getOtherScanTheCode()
}
window.getOtherScanTheCodeCallBack = (res: unknown) => {
  console.log('🚀 ~ res:', res)
}
</script>

<style lang="scss" scoped>
.mep-bridge-page {
  width: 100%;
  background: color(bg-secondary);
  font-family:
    -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: color(text-primary);
  line-height: 1.47;

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: space(4) space(4) space(6);
  }

  // 页面标题
  .page-title {
    @include card();

    .badge {
      background: color-alpha(indigo, 0.1);
      color: color(indigo);
    }
  }

  // 测试区域
  .test-section {
    padding: space(6);
    background: color(gray-50);
    border: 1px solid color(border-normal);
    border-radius: radius(xl);
    transition: transition(all-normal);

    &:hover {
      background: color(gray-100);
      border-color: color(border-dark);
    }

    .test-header {
      display: flex;
      align-items: flex-start;
      gap: space(4);
      margin-bottom: space(4);

      .test-icon {
        width: space(10);
        height: space(10);
        border-radius: radius(lg);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &.ios {
          background: color-alpha(primary, 0.1);
        }

        &.android {
          background: color-alpha(success, 0.1);
        }
      }

      .test-info {
        flex: 1;

        .test-title {
          font-size: font(base);
          font-weight: weight(semibold);
          color: color(text-primary);
          margin-bottom: space(1);
        }

        .test-desc {
          font-size: font(sm);
          color: color(text-secondary);
          line-height: 1.5;
        }
      }
    }

    .test-btn {
      @include button-primary();
      width: 100%;
      gap: space(2);

      &.ios-btn {
        @include button-primary();
        background: gradient(blue);
      }

      &.android-btn {
        @include button-primary();
        background: gradient(green);
      }
    }
  }

  // 回调信息
  .callback-info {
    h3 {
      font-size: font(base);
      font-weight: weight(semibold);
      color: color(text-primary);
      margin-bottom: space(3);
    }

    .info-box {
      display: flex;
      align-items: flex-start;
      padding: space(4);
      background: color-alpha(primary, 0.05);
      border: 1px solid color-alpha(primary, 0.1);
      border-radius: radius(lg);

      p {
        font-size: font(sm);
        color: color(text-secondary);
        line-height: 1.6;

        .code-inline {
          background: color(gray-100);
          padding: space(1) space(2);
          border-radius: radius(sm);
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: font(xs);
          color: color(text-primary);
        }
      }
    }
  }

  // 间距工具类
  .space-y-6 > * + * {
    margin-top: space(6);
  }

  // 动画效果

  // 响应式优化
  @include responsive(sm) {
    padding: 0;

    .test-header {
      flex-direction: column;
      align-items: flex-start;

      .test-icon {
        align-self: center;
      }

      .test-info {
        text-align: center;
      }
    }
  }
}

.mep-bridge-page {
  @include page-container();
}

.page-container {
  @include content-container();
}
</style>
