<template>
  <div class="wechat-miniprogram-page">
    <div class="page-container">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">微信小程序启动</h1>
        <p class="text-gray-600">正在启动微信小程序...</p>
        <div
          class="mt-4 inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium"
        >
          <SvgIcon name="wechat" size="16" class="mr-2" />
          微信小程序
        </div>
      </div>

      <!-- 加载状态卡片 -->
      <div class="max-w-lg mx-auto">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          <div
            class="px-6 py-5 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-lg font-semibold text-gray-900">启动状态</h2>
                <p class="text-sm text-gray-500 mt-1">正在处理启动请求</p>
              </div>
              <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <SvgIcon name="wechat" size="24" class="text-green-600" />
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <!-- 加载动画 -->
              <div class="loading-container">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在生成启动链接...</p>
              </div>

              <!-- 小程序信息 -->
              <div class="miniprogram-info">
                <h3 class="text-base font-semibold text-gray-900 mb-3">小程序信息</h3>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">AppID:</span>
                    <span class="info-value">wx22875a543119e48f</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">页面路径:</span>
                    <span class="info-value">packageA/pages/auth/life-auth</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">查询参数:</span>
                    <span class="info-value">id=123</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">环境版本:</span>
                    <span class="info-value">release</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
const launchWechatMiniProgram = new window.LaunchWechatMiniProgram({
  appId: 'wx22875a543119e48f',
  path: 'packageA/pages/auth/life-auth',
  query: 'id=123',
  envVersion: 'release',
  // serverUrl: 'https://api.example.com/getEncryptedUrlScheme',
})

onMounted(() => {
  const url = launchWechatMiniProgram.getDecryptedUrlScheme()
  console.log('🚀 ~ onMounted ~ url:', url)
  // console.log(url)
  window.location.href = url
})
</script>

<style lang="scss" scoped>
.wechat-miniprogram-page {
  width: 100%;
  background: color(bg-secondary);
  font-family:
    -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: color(text-primary);
  line-height: 1.47;

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: space(4) space(4) space(6);
  }

  // 页面标题
  .page-title {
    text-align: center;
    margin-bottom: space(8);

    .badge {
      background: color(success-100);
      color: color(success-600);
    }
  }

  // 加载容器
  .loading-container {
    text-align: center;
    padding: space(6) 0;

    .loading-spinner {
      @include loading-spinner(40px, success);
      margin: 0 auto space(4);
    }

    .loading-text {
      font-size: font(body-sm);
      color: color(text-secondary);
    }
  }

  // 小程序信息
  .miniprogram-info {
    h3 {
      font-size: font(body);
      font-weight: weight(semibold);
      color: color(text-primary);
      margin-bottom: space(3);
    }

    .info-grid {
      display: flex;
      flex-direction: column;
      gap: space(3);
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: space(3);
      background: color(neutral-50);
      border-radius: radius(md);

      .info-label {
        font-size: font(body-sm);
        font-weight: weight(medium);
        color: color(text-secondary);
        flex-shrink: 0;
        margin-right: space(3);
      }

      .info-value {
        font-size: font(body-sm);
        color: color(text-primary);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        text-align: right;
        word-break: break-all;
      }
    }
  }

  // 动画关键帧
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 响应式优化
  @include responsive(md) {
    padding: 0;

    .info-item {
      flex-direction: column;
      align-items: flex-start;

      .info-label {
        margin-bottom: space(1);
      }

      .info-value {
        text-align: left;
      }
    }
  }
}

.wechat-miniprogram-page {
  @include page-container();
}

.page-container {
  @include content-container();
}
</style>
