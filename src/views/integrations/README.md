# 第三方集成模块

## 📋 模块说明

本目录包含与第三方服务和平台集成相关的页面组件。

## 📁 页面列表

### LaunchWechatMiniProgram.vue
- **路由**: `/integrations/wechat-miniprogram`
- **功能**: 微信小程序启动
- **描述**: 测试和调试微信小程序的启动和交互功能

### MepBridge.vue
- **路由**: `/integrations/mep-bridge`
- **功能**: MEP Bridge 测试
- **描述**: 测试 MEP (Mobile Extension Platform) 桥接功能

## 🎨 模块特色

- **切换动画**: slide-up (上滑效果)
- **分组标识**: `group: 'integrations'`
- **主题色调**: 集成橙色系

## 🔧 开发指南

### 添加新页面

1. 在此目录下创建新的 Vue 组件
2. 在路由配置中添加对应路由：

```typescript
{
  path: 'integrations/new-platform',
  name: 'IntegrationsNewPlatform',
  component: () => import('../views/integrations/NewPlatform.vue'),
  meta: {
    title: '新平台集成',
    showBack: true,
    group: 'integrations',
  },
}
```

### 命名规范

- **文件名**: PascalCase (如 `NewPlatform.vue`)
- **路由名**: 带前缀的 PascalCase (如 `IntegrationsNewPlatform`)
- **路径**: kebab-case (如 `integrations/new-platform`)

## 🔗 集成开发指南

### 集成类型

1. **社交平台**: 微信、QQ、微博等
2. **支付平台**: 支付宝、微信支付等
3. **地图服务**: 高德、百度地图等
4. **云服务**: 阿里云、腾讯云等

### 最佳实践

1. **错误处理**: 完善的异常捕获和用户提示
2. **状态管理**: 清晰的集成状态跟踪
3. **配置管理**: 灵活的配置参数设置
4. **测试覆盖**: 全面的集成测试用例
