<template>
  <div class="sdk-jsbridge-methods-page">
    <div class="page-container">
      <!-- iOS 风格的导航栏 -->
      <div class="ios-header">
        <h1 class="ios-large-title">JS Bridge 方法</h1>
        <p class="ios-subtitle">测试各种 SDK 接口方法</p>
      </div>

      <!-- iOS 风格的分段控制器 -->
      <div class="ios-segment-control">
        <div class="ios-segment-container">
          <button
            v-for="(category, index) in methodCategories"
            :key="index"
            @click="setActiveCategory(category.name)"
            class="ios-segment-button"
            :class="{ active: activeCategory === category.name }"
          >
            <SvgIcon :name="getCategoryIcon(category.name)" size="16" class="ios-segment-icon" />
            {{ category.title }}
          </button>
        </div>
      </div>

      <!-- 现代化方法列表 -->
      <div class="px-4 py-6">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          <div
            class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-lg font-semibold text-gray-900">
                  {{ getCategoryTitle(activeCategory) }}
                </h2>
                <p class="text-sm text-gray-500 mt-1">
                  {{ getMethodsByCategory(activeCategory).length }} 个可用方法
                </p>
              </div>
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <SvgIcon :name="getCategoryIcon(activeCategory)" size="24" class="text-blue-600" />
              </div>
            </div>
          </div>

          <div class="divide-y divide-gray-50">
            <button
              v-for="(method, index) in getMethodsByCategory(activeCategory)"
              :key="index"
              @click="activeMethod = method.name"
              class="w-full text-left px-6 py-4 hover:bg-gray-50 transition-all duration-200 group"
              :class="activeMethod === method.name ? 'bg-blue-50 border-r-4 border-blue-500' : ''"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <h3
                    class="text-base font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors"
                  >
                    {{ method.title }}
                  </h3>
                  <p class="text-sm text-gray-500 font-mono mt-1 truncate">
                    {{ method.name }}
                  </p>
                  <div class="flex items-center mt-2">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600"
                    >
                      {{ method.params.length }} 个参数
                    </span>
                  </div>
                </div>
                <div class="flex items-center ml-4">
                  <div
                    v-if="activeMethod === method.name"
                    class="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse"
                  ></div>
                  <SvgIcon
                    name="chevron-left"
                    size="16"
                    class="text-gray-400 rotate-180 group-hover:text-blue-500 transition-colors"
                  />
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- 现代化测试区域 -->
      <div v-if="activeMethod" class="px-4 pb-6">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          <div
            class="px-6 py-5 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ getActiveMethodTitle() }}</h3>
                <p class="text-sm text-gray-500 font-mono mt-1">{{ activeMethod }}</p>
              </div>
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <SvgIcon name="play" size="20" class="text-green-600" />
              </div>
            </div>
          </div>

          <div class="p-6">
            <!-- 现代化参数表单 -->
            <div v-if="getActiveMethodParams().length > 0" class="space-y-6 mb-8">
              <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-gray-900">参数配置</h4>
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ getActiveMethodParams().length }} 个参数
                </span>
              </div>
              <div v-for="(param, idx) in getActiveMethodParams()" :key="idx" class="space-y-3">
                <label :for="param.name" class="block">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-900">
                      {{ param.title }}
                      <span v-if="param.required" class="text-red-500 ml-1">*</span>
                    </span>
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600"
                    >
                      {{ param.type }}
                    </span>
                  </div>
                </label>

                <!-- 现代化输入控件 -->
                <div v-if="param.type === 'Boolean'">
                  <select
                    :id="param.name"
                    v-model="formData[param.name]"
                    class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 appearance-none"
                  >
                    <option :value="true">是 (true)</option>
                    <option :value="false">否 (false)</option>
                  </select>
                </div>

                <div v-else-if="param.type === 'Number'">
                  <input
                    type="number"
                    :id="param.name"
                    v-model.number="formData[param.name]"
                    :placeholder="param.placeholder || '请输入数字'"
                    class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-sm text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200"
                  />
                </div>

                <div v-else-if="param.type === 'Object'" class="w-full">
                  <textarea
                    :id="param.name"
                    v-model="formData[param.name]"
                    rows="4"
                    :placeholder="param.placeholder || '请输入JSON格式的对象'"
                    class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-sm text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 font-mono resize-none"
                  ></textarea>
                </div>

                <div v-else-if="param.type === 'Array'" class="w-full">
                  <textarea
                    :id="param.name"
                    v-model="formData[param.name]"
                    rows="4"
                    :placeholder="param.placeholder || '请输入JSON格式的数组'"
                    class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-sm text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 font-mono resize-none"
                  ></textarea>
                </div>

                <div v-else>
                  <input
                    type="text"
                    :id="param.name"
                    v-model="formData[param.name]"
                    :placeholder="param.placeholder || '请输入文本'"
                    class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-sm text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200"
                  />
                </div>

                <!-- 参数说明 -->
                <div v-if="param.placeholder" class="mt-2 text-xs text-gray-500 leading-relaxed">
                  {{ param.placeholder }}
                </div>
              </div>
            </div>

            <!-- 现代化无参数提示 -->
            <div v-else class="text-center py-8">
              <div
                class="w-16 h-16 mx-auto mb-4 bg-blue-50 rounded-2xl flex items-center justify-center"
              >
                <SvgIcon name="settings" size="32" class="text-blue-500" />
              </div>
              <p class="text-sm text-gray-500">此方法无需参数配置</p>
              <p class="text-xs text-gray-400 mt-1">可以直接执行</p>
            </div>

            <!-- 现代化操作按钮 -->
            <div class="flex flex-col gap-4 pt-6 border-t border-gray-100">
              <button
                @click="executeMethod()"
                type="button"
                class="w-full flex items-center justify-center px-6 py-4 text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <SvgIcon name="play" size="18" class="mr-2 text-white" />
                执行方法
              </button>
              <button
                @click="clearForm()"
                type="button"
                class="w-full flex items-center justify-center px-6 py-3 text-sm font-medium rounded-xl text-gray-600 bg-gray-100 hover:bg-gray-200 focus:outline-none transition-all duration-200"
              >
                <SvgIcon name="trash" size="16" class="mr-2 text-gray-500" />
                清空表单
              </button>
            </div>

            <!-- 现代化执行结果 -->
            <div v-if="result" class="mt-8 pt-6 border-t border-gray-100">
              <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-semibold text-gray-900">执行结果</h4>
                <button
                  @click="copyResult()"
                  type="button"
                  class="flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-all duration-200"
                >
                  <SvgIcon name="clipboard" size="16" class="mr-2 text-blue-600" />
                  复制结果
                </button>
              </div>
              <div class="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
                <div class="p-4 max-h-80 overflow-auto">
                  <pre
                    class="text-sm text-gray-800 whitespace-pre-wrap break-words font-mono leading-relaxed"
                    >{{ resultDisplay }}</pre
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Vue API 现在通过 unplugin-auto-import 自动导入
// 无需手动导入 ref, computed, watch 等

// 布局现在由路由系统统一管理，无需在页面中导入

// 方法分类
const methodCategories = [
  {
    name: 'basic',
    title: '基础能力',
    description: '基础功能检测和通用能力',
  },
  {
    name: 'login',
    title: '登录相关',
    description: '登录、退出登录等功能',
  },
  {
    name: 'payment',
    title: '支付相关',
    description: '支付、扫码等功能',
  },
  {
    name: 'app',
    title: '应用信息',
    description: '获取应用版本、更新等',
  },
  {
    name: 'device',
    title: '设备信息',
    description: '获取设备信息、状态栏等',
  },
  {
    name: 'ui',
    title: 'UI交互',
    description: '全屏设置、弹窗、loading等',
  },
  {
    name: 'user',
    title: '用户信息',
    description: '获取用户资料、实名信息等',
  },
  {
    name: 'interaction',
    title: '应用交互',
    description: '打开其他应用、分享等',
  },
  {
    name: 'location',
    title: '地址和文件',
    description: '地址选择、文件选择、位置信息等',
  },
]

// SDK方法列表
const sdkMethods = [
  {
    name: 'canIUse',
    title: '判断API是否可用',
    category: 'basic',
    params: [
      {
        name: 'params',
        title: '方法名称',
        type: 'String',
        required: true,
        placeholder: '例如: login',
      },
    ],
  },
  {
    name: 'login',
    title: '登录',
    category: 'login',
    params: [],
  },
  {
    name: 'goLogin',
    title: '退出登录并打开登录页面',
    category: 'login',
    params: [
      {
        name: 'txt',
        title: '提示文字',
        type: 'String',
        required: true,
        placeholder: '例如: 登录已过期，请重新登录',
      },
    ],
  },
  {
    name: 'exit',
    title: '退出应用',
    category: 'login',
    params: [
      {
        name: 'isNeedConfirm',
        title: '是否需要二次确认',
        type: 'Boolean',
        required: false,
      },
    ],
  },
  {
    name: 'requestPayment',
    title: '支付',
    category: 'payment',
    params: [
      { name: 'orderNo', title: '订单号', type: 'String', required: true },
      { name: 'bizType', title: '业务类型', type: 'String', required: true },
    ],
  },
  {
    name: 'scanCode',
    title: '扫一扫',
    category: 'payment',
    params: [
      {
        name: 'onlyFromCamera',
        title: '是否只能从相机扫码',
        type: 'Boolean',
        required: false,
      },
      {
        name: 'scanType',
        title: '扫码类型',
        type: 'Array',
        required: false,
        placeholder: '例如: ["barCode", "qrCode"]',
      },
    ],
  },
  {
    name: 'getAppVersion',
    title: '获取应用当前版本号',
    category: 'app',
    params: [],
  },
  {
    name: 'updateApp',
    title: '更新APP',
    category: 'app',
    params: [],
  },
  {
    name: 'getDeviceInfo',
    title: '获取当前设备信息',
    category: 'device',
    params: [],
  },
  {
    name: 'getStatusBarHeight',
    title: '获取状态栏高度',
    category: 'device',
    params: [],
  },
  {
    name: 'getBottomSafeHeight',
    title: '获取底部安全区域高度',
    category: 'device',
    params: [],
  },
  {
    name: 'getCapsulePosition',
    title: '获取胶囊的位置信息',
    category: 'device',
    params: [],
  },
  {
    name: 'setStatusBarTheme',
    title: '设置状态栏样式',
    category: 'ui',
    params: [
      {
        name: 'theme',
        title: '状态栏主题',
        type: 'String',
        required: true,
        placeholder: 'light或dark',
      },
    ],
  },
  {
    name: 'setFullScreen',
    title: '设置是否全屏',
    category: 'ui',
    params: [{ name: 'fullScreen', title: '是否全屏', type: 'Boolean', required: true }],
  },
  {
    name: 'hideProgramLoading',
    title: '隐藏loading',
    category: 'ui',
    params: [],
  },
  {
    name: 'showModal',
    title: '显示原生toast/弹窗',
    category: 'ui',
    params: [
      {
        name: 'style',
        title: '提示类型',
        type: 'String',
        required: true,
        placeholder: 'toast或alert',
      },
      { name: 'content', title: '提示内容', type: 'String', required: true },
      { name: 'btnText', title: '按钮文字', type: 'String', required: false },
    ],
  },
  {
    name: 'getPhoneNumber',
    title: '获取纷享生活用户手机号',
    category: 'user',
    params: [],
  },
  {
    name: 'getRealUserInfo',
    title: '获取生活用户实名信息',
    category: 'user',
    params: [],
  },
  {
    name: 'getUserProfile',
    title: '获取昵称头像',
    category: 'user',
    params: [],
  },
  {
    name: 'openMiniProgram',
    title: '打开其他应用',
    category: 'interaction',
    params: [
      { name: 'appId', title: '应用ID', type: 'String', required: true },
      {
        name: 'extraData',
        title: '额外数据',
        type: 'Object',
        required: false,
        placeholder: '例如: {"key": "value"}',
      },
    ],
  },
  {
    name: 'openCustomerServiceChat',
    title: '打开纷享生活客服',
    category: 'interaction',
    params: [
      { name: 'corpId', title: '企业ID', type: 'String', required: true },
      { name: 'userId', title: '用户ID', type: 'String', required: true },
      { name: 'nickname', title: '用户昵称', type: 'String', required: false },
      { name: 'avatar', title: '用户头像', type: 'String', required: false },
    ],
  },
  {
    name: 'shareMessageToExternalApp',
    title: '分享内容至外部应用',
    category: 'interaction',
    params: [
      {
        name: 'type',
        title: '分享类型',
        type: 'String',
        required: true,
        placeholder: 'text、image或link',
      },
      { name: 'title', title: '分享标题', type: 'String', required: false },
      { name: 'desc', title: '分享描述', type: 'String', required: false },
      { name: 'link', title: '分享链接', type: 'String', required: false },
      { name: 'imageUrl', title: '分享图片', type: 'String', required: false },
    ],
  },
  {
    name: 'chooseAddress',
    title: '打开原生地址选择',
    category: 'location',
    params: [
      {
        name: 'needAuthConfirm',
        title: '是否每次都显示授权弹框',
        type: 'Boolean',
        required: false,
      },
    ],
  },
  {
    name: 'getDefaultAddress',
    title: '获取默认地址',
    category: 'location',
    params: [],
  },
  {
    name: 'chooseFile',
    title: '选择文件',
    category: 'location',
    params: [
      {
        name: 'fileType',
        title: '文件类型',
        type: 'String',
        required: false,
        placeholder: '例如: image, video, document 等，不传则选择所有文件类型',
      },
    ],
  },
  {
    name: 'getLocationInfo',
    title: '获取经纬度信息',
    category: 'location',
    params: [],
  },
]

// 当前选中的分类和方法
const activeCategory = ref('basic')
const activeMethod = ref('')
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const formData = ref<Record<string, any>>({})
const result = ref<unknown>(null)

// 结果展示
const resultDisplay = computed(() => {
  return typeof result.value === 'object' ? JSON.stringify(result.value, null, 2) : result.value
})

// 设置当前分类
const setActiveCategory = (category: string) => {
  activeCategory.value = category
  // 默认选中该分类下的第一个方法
  const methodsInCategory = getMethodsByCategory(category)
  if (methodsInCategory.length > 0) {
    activeMethod.value = methodsInCategory[0].name
  } else {
    activeMethod.value = ''
  }
}

// 获取分类标题
const getCategoryTitle = (categoryName: string) => {
  const category = methodCategories.find((c) => c.name === categoryName)
  return category ? category.title : ''
}

// 获取分类图标
const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    basic: 'home',
    ui: 'document',
    device: 'settings',
    network: 'external-link',
    storage: 'shield',
    media: 'play',
    location: 'lightning',
    payment: 'credit-card',
    social: 'wechat',
    other: 'code',
  }
  return iconMap[category] || 'settings'
}

// 获取当前分类下的方法
const getMethodsByCategory = (category: string) => {
  return sdkMethods.filter((m) => m.category === category)
}

// 获取当前选中的方法标题
const getActiveMethodTitle = () => {
  const method = sdkMethods.find((m) => m.name === activeMethod.value)
  return method ? method.title : ''
}

// 获取当前选中方法的参数列表
const getActiveMethodParams = () => {
  const method = sdkMethods.find((m) => m.name === activeMethod.value)
  return method ? method.params : []
}

// 清空表单
const clearForm = () => {
  formData.value = {}
  result.value = null
}

// 复制结果
const copyResult = async () => {
  try {
    await navigator.clipboard.writeText(String(resultDisplay.value))
    // 这里可以添加一个toast提示
    console.log('结果已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 执行方法
const executeMethod = () => {
  if (!activeMethod.value || !window.fx || !(activeMethod.value in window.fx)) {
    result.value = { error: `🚀 ~方法 ${activeMethod.value} 不存在` }
    return
  }

  // 处理参数
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const params: Record<string, any> = { ...formData.value }

  // 处理JSON字符串转对象
  for (const key in params) {
    if (typeof params[key] === 'string') {
      try {
        // 尝试解析JSON字符串
        if (params[key].startsWith('[') || params[key].startsWith('{')) {
          params[key] = JSON.parse(params[key])
        }
      } catch (e) {
        // 解析失败则保持原样
        console.error(`🚀 ~解析失败:`, e)
      }
    }
  }

  // 添加回调函数
  params.success = (res: unknown) => {
    console.log(`${activeMethod.value} success:`, res)
    result.value = { status: 'success', data: res }
  }

  params.fail = (res: unknown) => {
    console.log(`${activeMethod.value} fail:`, res)
    result.value = { status: 'fail', data: res }
  }

  params.complete = () => {
    console.log(`${activeMethod.value} complete`)
  }

  // 执行方法
  try {
    console.log(`Executing ${activeMethod.value} with params:`, params)
    // @ts-expect-error 动态调用SDK方法
    window.fx[activeMethod.value](params)
  } catch (error) {
    console.error('执行方法出错:', error)
    result.value = { error: `执行出错: ${error}` }
  }
}

// 监听方法变化，清空表单
watch(activeMethod, () => {
  clearForm()
})

// 页面初始化时，选择第一个分类和对应的第一个方法
setActiveCategory(methodCategories[0].name)
</script>

<style lang="scss" scoped>
// Apple 风格 JS Bridge 方法页面
.sdk-jsbridge-methods-page {
  @include ios-page-container();
}

.page-container {
  @include ios-content-container();
}

// Apple 风格头部
.ios-header {
  padding: space(8) 0 space(6);
  text-align: center;

  .ios-large-title {
    font-size: font(largeTitle);
    font-weight: weight(bold);
    color: color(text-primary);
    margin-bottom: space(2);
    letter-spacing: -0.02em;
  }

  .ios-subtitle {
    font-size: font(title3);
    font-weight: weight(regular);
    color: color(text-secondary);
  }
}

// Apple 风格分段控制器
.ios-segment-control {
  margin-bottom: space(6);

  .ios-segment-container {
    background: color(gray-100);
    border-radius: radius(lg);
    padding: space(1);
    display: flex;
    overflow-x: auto;
    gap: space(1);

    // 隐藏滚动条
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .ios-segment-button {
    flex-shrink: 0;
    padding: space(2) space(4);
    border-radius: radius(md);
    font-size: font(subheadline);
    font-weight: weight(medium);
    color: color(text-secondary);
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-out;
    display: flex;
    align-items: center;
    gap: space(2);
    white-space: nowrap;
    min-height: 32px;

    .ios-segment-icon {
      color: color(text-tertiary);
    }

    &.active {
      background: color(surface-primary);
      color: color(text-primary);
      box-shadow: shadow(sm);

      .ios-segment-icon {
        color: color(primary);
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 响应式适配
@include responsive(md) {
  .ios-container {
    padding: 0 space(3);
  }

  .ios-header {
    padding: space(6) 0 space(4);

    .ios-large-title {
      font-size: font(h1);
    }
  }
}
</style>
