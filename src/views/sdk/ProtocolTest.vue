<template>
  <div class="protocol-test-page">
    <div class="container mx-auto px-4 py-6">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">原生协议测试</h1>
        <p class="text-gray-600">测试各种原生协议的支持情况</p>
        <div
          class="mt-4 inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium"
        >
          <SvgIcon name="external-link" size="16" class="mr-2" />
          {{ protocols.length }} 个协议
        </div>
      </div>

      <!-- 协议测试网格 -->
      <div class="protocol-grid">
        <div v-for="protocol in protocols" :key="protocol.name" class="protocol-card">
          <div class="protocol-header">
            <div class="protocol-icon">
              <SvgIcon name="external-link" size="20" class="text-blue-600" />
            </div>
            <div class="protocol-info">
              <h3 class="protocol-name">{{ protocol.name }}</h3>
              <p class="protocol-description">{{ protocol.description }}</p>
            </div>
          </div>

          <div class="protocol-actions">
            <button @click="testProtocol(protocol.link)" class="test-btn">
              <SvgIcon name="play" size="16" class="mr-2" />
              测试协议
            </button>
            <div class="protocol-link">
              <code>{{ protocol.link }}</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const protocols = ref([
  { name: '电话 (tel:)', link: 'tel:+123456789', description: '拨打电话到 +123456789' },
  { name: '短信 (sms:)', link: 'sms:+123456789', description: '发送短信到 +123456789' },
  {
    name: '邮件 (mailto:)',
    link: 'mailto:<EMAIL>',
    description: '打开邮件客户端发送邮件',
  },
  { name: '网页 (http:)', link: 'http://baidu.com', description: '打开 http://baidu.com' },
  {
    name: '安全网页 (https:)',
    link: 'https://baidu.com',
    description: '打开 https://baidu.com',
  },
  { name: 'FTP (ftp:)', link: 'ftp://ftp.example.com', description: '访问 FTP 资源' },
  {
    name: '本地文件 (file:)',
    link: 'file:///C:/path/to/file.txt',
    description: '尝试访问本地文件',
  },
  {
    name: '嵌入数据 (data:)',
    link: 'data:text/plain;base64,SGVsbG8gd29ybGQh',
    description: '嵌入数据资源 (Base64 编码)',
  },
  {
    name: '地图 (geo:)',
    link: 'geo:37.7749,-122.4194',
    description: '打开地图定位到 San Francisco',
  },
  {
    name: '系统内容 (content:)',
    link: 'content://contacts/people/1',
    description: '访问系统内容提供器 (Android)',
  },
  {
    name: '应用商店 (market:)',
    link: 'market://details?id=com.zto.life',
    description: '打开 Android 应用商店',
  },
  {
    name: 'iOS 应用商店 (itms-apps:)',
    link: 'https://itunes.apple.com/cn/app/hua-rui-wei-yin-xing/id1054902115',
    description: '打开 iOS 应用商店',
  },
  {
    name: 'JavaScript (javascript:)',
    link: "javascript:alert('Hello, World!')",
    description: '执行 JavaScript 代码',
  },
])

function testProtocol(link: string) {
  // 打开协议
  window.location.href = link
}
</script>

<style lang="scss" scoped>
.protocol-test-page {
  width: 100%;
  background: color(bg-secondary);
  font-family:
    -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: color(text-primary);
  line-height: 1.47;

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: space(4) space(4) space(6);
  }

  // 页面标题
  .page-title {
    text-align: center;
    margin-bottom: space(8);

    .badge {
      background: color(primary-100);
      color: color(primary-600);
    }
  }

  // 协议网格
  .protocol-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: space(6);
    max-width: 80rem;
    margin: 0 auto;
  }

  // 协议卡片
  .protocol-card {
    @include card();
    border: 1px solid color(border-light);
    overflow: hidden;
    transition: transition(normal);

    &:hover {
      transform: translateY(-2px);
      box-shadow: shadow(lg);
    }

    .protocol-header {
      padding: space(6);
      background: color(neutral-50);
      border-bottom: 1px solid color(border-light);

      display: flex;
      align-items: flex-start;
      gap: space(4);

      .protocol-icon {
        width: space(10);
        height: space(10);
        background: color(primary-100);
        border-radius: radius(lg);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .protocol-info {
        flex: 1;
        min-width: 0;

        .protocol-name {
          font-size: font(body);
          font-weight: weight(semibold);
          color: color(text-primary);
          margin-bottom: space(1);
        }

        .protocol-description {
          font-size: font(body-sm);
          color: color(text-secondary);
          line-height: 1.5;
        }
      }
    }

    .protocol-actions {
      padding: space(6);

      .test-btn {
        @include button-primary();
        background: gradient(primary);
        width: 100%;
        gap: space(2);
        margin-bottom: space(4);
      }

      .protocol-link {
        background: color(neutral-50);
        border: 1px solid color(border-normal);
        border-radius: radius(md);
        padding: space(3);

        code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: font(caption);
          color: color(text-secondary);
          word-break: break-all;
          line-height: 1.4;
        }
      }
    }
  }

  // 响应式优化
  @include responsive(md) {
    padding: 0;

    .protocol-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
