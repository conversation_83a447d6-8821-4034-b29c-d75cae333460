# SDK 调试模块

## 📋 模块说明

本目录包含 SDK 调试相关的页面组件。

## 📁 页面列表

### JsBridgeMethods.vue
- **路由**: `/sdk/methods`
- **功能**: SDK 方法调试工具
- **描述**: 提供各种 SDK 方法的测试和调试功能

### ProtocolTest.vue
- **路由**: `/sdk/protocol`
- **功能**: 协议测试
- **描述**: 测试和验证各种协议接口的通信功能

## 🎨 模块特色

- **切换动画**: slide-fade (滑动效果)
- **分组标识**: `group: 'sdk'`
- **主题色调**: 技术蓝色系

## 🔧 开发指南

### 添加新页面

1. 在此目录下创建新的 Vue 组件
2. 在路由配置中添加对应路由：

```typescript
{
  path: 'sdk/new-feature',
  name: 'SdkNewFeature',
  component: () => import('../views/sdk/NewFeature.vue'),
  meta: {
    title: '新功能',
    showBack: true,
    group: 'sdk',
  },
}
```

### 命名规范

- **文件名**: PascalCase (如 `NewFeature.vue`)
- **路由名**: 带前缀的 PascalCase (如 `SdkNewFeature`)
- **路径**: kebab-case (如 `sdk/new-feature`)
