<!--
 * @Author: houbaoguo
 * @Date: 2024-12-17 09:16:01
 * @Description:
 * @LastEditTime: 2025-04-29 11:22:11
 * @LastEditors: houbaoguo
-->
<template>
  <div class="home-page">
    <div class="page-container">
      <!-- iOS 风格的大标题 -->
      <div class="page-header">
        <h1 class="ios-large-title">开发调试平台</h1>
        <p class="ios-subtitle">JS SDK 调试工具集</p>
      </div>

      <!-- iOS 风格的功能列表 -->
      <div class="ios-section">
        <div class="ios-section-header">
          <h2 class="ios-section-title">功能模块</h2>
        </div>
        <div class="ios-list-container">
          <router-link to="/dev/test-one" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-blue">
                <SvgIcon name="clipboard" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">调试资源收集</h3>
                <p class="ios-list-subtitle">收集调试所需的资源</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>

          <router-link to="/sdk/protocol" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-green">
                <SvgIcon name="document" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">调试协议</h3>
                <p class="ios-list-subtitle">协议接口调试</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>

          <router-link to="/features/web-capacity" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-purple">
                <SvgIcon name="shield" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">调试原生API限制</h3>
                <p class="ios-list-subtitle">检测API访问限制</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>

          <router-link to="/sdk/methods" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-yellow">
                <SvgIcon name="code" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">SDK方法测试</h3>
                <p class="ios-list-subtitle">测试SDK接口方法</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>

          <router-link to="/integrations/mep-bridge" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-indigo">
                <SvgIcon name="lightning" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">MEP桥接</h3>
                <p class="ios-list-subtitle">MEP桥接功能测试</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>

          <router-link to="/features/page-jump" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-indigo">
                <SvgIcon name="external-link" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">页面跳转</h3>
                <p class="ios-list-subtitle">页面跳转功能测试</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>
          <router-link to="/integrations/wechat-miniprogram" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-green">
                <SvgIcon name="wechat" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">启动微信小程序</h3>
                <p class="ios-list-subtitle">启动微信小程序功能测试</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>

          <router-link to="/dev/test-two" class="ios-list-item">
            <div class="ios-list-content">
              <div class="ios-icon-container system-orange">
                <SvgIcon name="settings" size="22" class="ios-icon" />
              </div>
              <div class="ios-text-content">
                <h3 class="ios-list-title">开发测试页面 2</h3>
                <p class="ios-list-subtitle">开发和调试用的测试页面</p>
              </div>
              <div class="ios-chevron">
                <SvgIcon name="chevron-right" size="16" class="ios-chevron-icon" />
              </div>
            </div>
          </router-link>
        </div>
      </div>

      <!-- iOS 风格的收银台测试 -->
      <div class="ios-section">
        <div class="ios-section-header">
          <h2 class="ios-section-title">收银台测试</h2>
        </div>
        <div class="ios-card">
          <div class="ios-form-group">
            <label class="ios-form-label">订单号</label>
            <input v-model="orderNo" type="text" placeholder="请输入订单号" class="ios-input" />
          </div>
          <button @click="test" class="ios-button-primary">
            <SvgIcon name="credit-card" size="18" class="mr-2" />
            启动收银台
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const orderNo = ref('')

const test = () => {
  window.fx?.requestPayment({
    orderNumber: orderNo.value,
    // bizType: "PAY",
    // timeout: 60000,
    success: (res: unknown) => {
      console.log('requestPayment success', res)
    },
    fail: (err: unknown) => {
      console.log('requestPayment fail', err)
    },
    complete: () => {
      console.log('requestPayment complete')
    },
  })
}
</script>

<style lang="scss" scoped>
// iOS 风格的样式
.home-page {
  @include ios-page-container();
}

.page-container {
  @include ios-content-container();
}

// iOS 风格的头部
.page-header {
  @include ios-page-header();
}

// iOS 风格的分组
.ios-section {
  margin-bottom: spacing(8);

  .ios-section-header {
    padding: 0 spacing(4) spacing(2);

    .ios-section-title {
      font-size: font-size(footnote);
      font-weight: font-weight(regular);
      color: color(secondary-label);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

// iOS 风格的列表容器
.ios-list-container {
  background: color(secondary-system-grouped-background);
  border-radius: radius(base);
  overflow: hidden;
  box-shadow: shadow(ios-card);
}

// iOS 风格的列表项
.ios-list-item {
  @include ios-list-item();
  display: block;
  text-decoration: none;

  .ios-list-content {
    display: flex;
    align-items: center;
    gap: spacing(3);

    .ios-icon-container {
      width: 32px;
      height: 32px;
      border-radius: radius(sm);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .ios-icon {
        color: white;
      }

      // 系统颜色
      &.system-blue {
        background: color(system-blue);
      }
      &.system-green {
        background: color(system-green);
      }
      &.system-purple {
        background: color(system-purple);
      }
      &.system-yellow {
        background: color(system-yellow);
      }
      &.system-indigo {
        background: color(system-indigo);
      }
      &.system-orange {
        background: color(system-orange);
      }
    }

    .ios-text-content {
      flex: 1;
      min-width: 0;

      .ios-list-title {
        font-size: font-size(body);
        font-weight: font-weight(regular);
        color: color(label);
        margin-bottom: spacing(1);
      }

      .ios-list-subtitle {
        font-size: font-size(footnote);
        font-weight: font-weight(regular);
        color: color(secondary-label);
        line-height: 1.3;
      }
    }

    .ios-chevron {
      .ios-chevron-icon {
        color: color(tertiary-label);
      }
    }
  }
}

// iOS 风格的卡片
.ios-card {
  @include ios-card();

  .ios-form-group {
    margin-bottom: spacing(4);

    .ios-form-label {
      display: block;
      font-size: font-size(footnote);
      font-weight: font-weight(regular);
      color: color(label);
      margin-bottom: spacing(2);
    }

    .ios-input {
      @include ios-input-base();
    }
  }

  .ios-button-primary {
    @include ios-button-primary();
    width: 100%;
    justify-content: center;
    gap: spacing(2);
  }
}

// 响应式适配已在混合器中处理
</style>
