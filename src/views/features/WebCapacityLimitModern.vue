<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-04-29 11:20:24
 * @Description: 现代化设计的 Web 能力限制测试
 * @LastEditTime: 2025-06-05 14:01:57
 * @LastEditors: houbaoguo
-->
<template>
  <div class="features-web-capacity-page">
    <div class="page-container">
      <!-- 现代化头部 -->
      <div class="modern-header">
        <div class="header-content">
          <h1 class="main-title">Web能力限制测试</h1>
          <p class="subtitle">检测和测试各种Web API的访问限制</p>
          <div class="header-badge">
            <SvgIcon name="shield" size="16" />
            <span>14 项能力测试</span>
          </div>
        </div>
      </div>

      <!-- 现代化测试网格 -->
      <div class="tests-grid">
        <!-- 文件上传测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-blue">
              <SvgIcon name="document" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">文件上传测试</h3>
              <p class="test-desc">测试文件选择和上传功能</p>
            </div>
          </div>
          <div class="test-content">
            <div class="form-group">
              <label class="form-label">单文件选择</label>
              <input type="file" class="file-input" @change="handleFileUpload" />
            </div>
            <button @click="testFileUpload" class="test-button system-blue">
              <SvgIcon name="document" size="16" />
              测试文件上传
            </button>
          </div>
        </div>

        <!-- 摄像头测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-green">
              <SvgIcon name="camera" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">摄像头测试</h3>
              <p class="test-desc">测试摄像头访问权限</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testCamera" class="test-button system-green">
              <SvgIcon name="camera" size="16" />
              测试摄像头
            </button>
          </div>
        </div>

        <!-- 麦克风测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-purple">
              <SvgIcon name="microphone" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">麦克风测试</h3>
              <p class="test-desc">测试麦克风访问权限</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testMicrophone" class="test-button system-purple">
              <SvgIcon name="microphone" size="16" />
              测试麦克风
            </button>
          </div>
        </div>

        <!-- 地理位置测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-orange">
              <SvgIcon name="location" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">地理位置测试</h3>
              <p class="test-desc">测试地理位置访问权限</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testGeolocation" class="test-button system-orange">
              <SvgIcon name="location" size="16" />
              测试地理位置
            </button>
          </div>
        </div>

        <!-- 通知测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-red">
              <SvgIcon name="bell" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">通知测试</h3>
              <p class="test-desc">测试浏览器通知权限</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testNotification" class="test-button system-red">
              <SvgIcon name="bell" size="16" />
              测试通知
            </button>
          </div>
        </div>

        <!-- 剪贴板测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-teal">
              <SvgIcon name="clipboard" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">剪贴板测试</h3>
              <p class="test-desc">测试剪贴板读写权限</p>
            </div>
          </div>
          <div class="test-content">
            <div class="form-group">
              <input
                v-model="clipboardText"
                type="text"
                placeholder="输入要复制的文本"
                class="text-input"
              />
            </div>
            <div class="button-group">
              <button @click="testClipboardWrite" class="test-button system-teal">
                <SvgIcon name="copy" size="16" />
                复制文本
              </button>
              <button @click="testClipboardRead" class="test-button-secondary">
                <SvgIcon name="paste" size="16" />
                读取剪贴板
              </button>
            </div>
          </div>
        </div>

        <!-- 全屏测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-indigo">
              <SvgIcon name="expand" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">全屏测试</h3>
              <p class="test-desc">测试全屏API功能</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testFullscreen" class="test-button system-indigo">
              <SvgIcon name="expand" size="16" />
              进入全屏
            </button>
          </div>
        </div>

        <!-- 设备方向测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-yellow">
              <SvgIcon name="rotate" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">设备方向测试</h3>
              <p class="test-desc">测试设备方向传感器</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testDeviceOrientation" class="test-button system-yellow">
              <SvgIcon name="rotate" size="16" />
              测试设备方向
            </button>
          </div>
        </div>

        <!-- 振动测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-pink">
              <SvgIcon name="vibrate" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">振动测试</h3>
              <p class="test-desc">测试设备振动功能</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testVibration" class="test-button system-pink">
              <SvgIcon name="vibrate" size="16" />
              测试振动
            </button>
          </div>
        </div>

        <!-- 电池状态测试 -->
        <div class="test-card">
          <div class="test-header">
            <div class="test-icon system-green">
              <SvgIcon name="battery" size="24" />
            </div>
            <div class="test-info">
              <h3 class="test-title">电池状态测试</h3>
              <p class="test-desc">测试电池状态API</p>
            </div>
          </div>
          <div class="test-content">
            <button @click="testBattery" class="test-button system-green">
              <SvgIcon name="battery" size="16" />
              测试电池状态
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const clipboardText = ref('Hello, World!')

// 文件上传测试
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    console.log('选择的文件:', files[0])
  }
}

const testFileUpload = () => {
  console.log('测试文件上传功能')
}

// 摄像头测试
const testCamera = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ video: true })
    console.log('摄像头访问成功')
    stream.getTracks().forEach((track) => track.stop())
  } catch (error) {
    console.error('摄像头访问被限制:', error)
  }
}

// 麦克风测试
const testMicrophone = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    console.log('麦克风访问成功')
    stream.getTracks().forEach((track) => track.stop())
  } catch (error) {
    console.error('麦克风访问被限制:', error)
  }
}

// 地理位置测试
const testGeolocation = () => {
  if ('geolocation' in navigator) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('地理位置获取成功:', position.coords)
      },
      (error) => {
        console.error('地理位置获取被限制:', error)
      },
    )
  } else {
    console.error('浏览器不支持地理位置API')
  }
}

// 通知测试
const testNotification = async () => {
  if ('Notification' in window) {
    const permission = await Notification.requestPermission()
    if (permission === 'granted') {
      new Notification('测试通知', {
        body: '这是一个测试通知',
        icon: '/favicon.ico',
      })
      console.log('通知发送成功')
    } else {
      console.error('通知权限被拒绝')
    }
  } else {
    console.error('浏览器不支持通知API')
  }
}

// 剪贴板测试
const testClipboardWrite = async () => {
  try {
    await navigator.clipboard.writeText(clipboardText.value)
    console.log('文本已复制到剪贴板')
  } catch (error) {
    console.error('剪贴板写入被限制:', error)
  }
}

const testClipboardRead = async () => {
  try {
    const text = await navigator.clipboard.readText()
    console.log('从剪贴板读取的文本:', text)
    clipboardText.value = text
  } catch (error) {
    console.error('剪贴板读取被限制:', error)
  }
}

// 全屏测试
const testFullscreen = async () => {
  try {
    await document.documentElement.requestFullscreen()
    console.log('进入全屏模式')
  } catch (error) {
    console.error('全屏模式被限制:', error)
  }
}

// 设备方向测试
const testDeviceOrientation = () => {
  if ('DeviceOrientationEvent' in window) {
    const handleOrientation = (event: DeviceOrientationEvent) => {
      console.log('设备方向:', {
        alpha: event.alpha,
        beta: event.beta,
        gamma: event.gamma,
      })
    }

    window.addEventListener('deviceorientation', handleOrientation, { once: true })
    console.log('开始监听设备方向')
  } else {
    console.error('浏览器不支持设备方向API')
  }
}

// 振动测试
const testVibration = () => {
  if ('vibrate' in navigator) {
    navigator.vibrate([200, 100, 200])
    console.log('设备振动')
  } else {
    console.error('浏览器不支持振动API')
  }
}

// 电池状态测试
const testBattery = async () => {
  try {
    // @ts-ignore
    const battery = await navigator.getBattery()
    console.log('电池状态:', {
      charging: battery.charging,
      level: battery.level,
      chargingTime: battery.chargingTime,
      dischargingTime: battery.dischargingTime,
    })
  } catch (error) {
    console.error('电池状态API不可用:', error)
  }
}
</script>

<style lang="scss" scoped>
.features-web-capacity-page {
  @include modern-page-container();
  background: linear-gradient(135deg, color(bg-secondary) 0%, color(purple-50) 100%);
}

.page-container {
  @include content-container();
}

// 现代化头部
.modern-header {
  text-align: center;
  margin-bottom: space(12);

  .header-content {
    .main-title {
      font-size: font(display);
      font-weight: weight(bold);
      color: color(text-primary);
      margin-bottom: space(4);
      background: linear-gradient(135deg, color(primary) 0%, color(purple) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      font-size: font(body-lg);
      color: color(text-secondary);
      margin-bottom: space(6);
    }

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: space(2);
      padding: space(2) space(4);
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border-radius: radius(full);
      font-size: font(body-sm);
      color: color(text-secondary);
      border: 1px solid rgba(255, 255, 255, 0.2);

      svg {
        color: color(purple);
      }
    }
  }
}

// 测试网格
.tests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: space(6);
}

// 测试卡片
.test-card {
  @include glass-card();

  .test-header {
    display: flex;
    align-items: flex-start;
    gap: space(4);
    margin-bottom: space(4);
    padding-bottom: space(4);
    border-bottom: 1px solid color(border-light);

    .test-icon {
      width: 48px;
      height: 48px;
      border-radius: radius(lg);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      svg {
        color: white;
      }

      // 系统颜色
      &.system-blue {
        background: color(primary);
      }
      &.system-green {
        background: color(success);
      }
      &.system-purple {
        background: color(purple);
      }
      &.system-red {
        background: color(error);
      }
      &.system-orange {
        background: color(warning);
      }
      &.system-teal {
        background: color(info);
      }
      &.system-indigo {
        background: color(primary-700);
      }
      &.system-yellow {
        background: color(warning);
      }
      &.system-pink {
        background: color(purple-500);
      }
    }

    .test-info {
      flex: 1;

      .test-title {
        font-size: font(h4);
        font-weight: weight(semibold);
        color: color(text-primary);
        margin-bottom: space(1);
      }

      .test-desc {
        font-size: font(body-sm);
        color: color(text-secondary);
        line-height: 1.5;
      }
    }
  }

  .test-content {
    .form-group {
      margin-bottom: space(4);

      .form-label {
        display: block;
        font-size: font(body-sm);
        font-weight: weight(medium);
        color: color(text-primary);
        margin-bottom: space(2);
      }

      .file-input,
      .text-input {
        @include input();
        font-size: font(body-sm);
      }
    }

    .button-group {
      display: flex;
      gap: space(3);
      flex-wrap: wrap;
    }

    .test-button {
      @include button-primary();
      gap: space(2);
      flex: 1;
      min-width: 140px;

      &.system-blue {
        background: color(primary);
      }
      &.system-green {
        background: color(success);
      }
      &.system-purple {
        background: color(purple);
      }
      &.system-red {
        background: color(error);
      }
      &.system-orange {
        background: color(warning);
      }
      &.system-teal {
        background: color(info);
      }
      &.system-indigo {
        background: color(primary-700);
      }
      &.system-yellow {
        background: color(warning);
      }
      &.system-pink {
        background: color(purple-500);
      }
    }

    .test-button-secondary {
      @include button-secondary();
      gap: space(2);
      flex: 1;
      min-width: 140px;
    }
  }
}

// 响应式设计
@include responsive(lg) {
  .tests-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: space(4);
  }
}

@include responsive(md) {
  .modern-container {
    padding: space(4) space(3);
  }

  .modern-header {
    margin-bottom: space(8);

    .header-content .main-title {
      font-size: font(h1);
    }
  }

  .tests-grid {
    grid-template-columns: 1fr;
    gap: space(4);
  }

  .test-content .button-group {
    flex-direction: column;

    .test-button,
    .test-button-secondary {
      width: 100%;
    }
  }
}
</style>
