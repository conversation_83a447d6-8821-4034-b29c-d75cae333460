# 功能测试模块

## 📋 模块说明

本目录包含各种功能特性测试相关的页面组件。

## 📁 页面列表

### WebCapacityLimit.vue
- **路由**: `/features/web-capacity`
- **功能**: Web能力限制测试
- **描述**: 检测和测试Web API的访问限制和能力边界

### PageJump.vue
- **路由**: `/features/page-jump`
- **功能**: 页面跳转测试
- **描述**: 测试各种页面跳转方式和路由导航功能

## 🎨 模块特色

- **切换动画**: fade-scale (缩放效果)
- **分组标识**: `group: 'features'`
- **主题色调**: 功能绿色系

## 🔧 开发指南

### 添加新页面

1. 在此目录下创建新的 Vue 组件
2. 在路由配置中添加对应路由：

```typescript
{
  path: 'features/new-test',
  name: 'FeaturesNewTest',
  component: () => import('../views/features/NewTest.vue'),
  meta: {
    title: '新测试功能',
    showBack: true,
    group: 'features',
  },
}
```

### 命名规范

- **文件名**: PascalCase (如 `NewTest.vue`)
- **路由名**: 带前缀的 PascalCase (如 `FeaturesNewTest`)
- **路径**: kebab-case (如 `features/new-test`)

## 💡 功能测试指南

### 测试类型

1. **API 测试**: 测试各种 Web API 的可用性
2. **兼容性测试**: 测试不同环境下的功能表现
3. **性能测试**: 测试功能的性能指标
4. **边界测试**: 测试功能的极限情况
