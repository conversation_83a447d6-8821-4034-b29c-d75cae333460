<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-04-29 11:20:24
 * @Description: 设计的页面跳转测试
 * @LastEditTime: 2025-06-05 14:01:57
 * @LastEditors: houbaoguo
-->
<template>
  <div class="page-jump-page">
    <div class="page-container">
      <!-- 头部 -->
      <div class="modern-header">
        <div class="header-content">
          <h1 class="main-title">页面跳转测试</h1>
          <p class="subtitle">测试不同的页面跳转方式</p>
          <div class="header-badge">
            <SvgIcon name="external-link" size="16" />
            <span>3 种跳转方式</span>
          </div>
        </div>
      </div>

      <!-- 跳转方法卡片 -->
      <div class="methods-grid">
        <!-- location.href 跳转 -->
        <div class="method-card primary">
          <div class="card-header">
            <div class="method-icon">
              <SvgIcon name="external-link" size="28" />
            </div>
            <div class="method-info">
              <h3 class="method-title">location.href</h3>
              <p class="method-desc">当前窗口跳转，会在浏览器历史中留下记录</p>
            </div>
            <div class="method-badge same-window">同窗口</div>
          </div>
          <div class="card-content">
            <p class="method-detail">
              使用 <code>window.location.href</code> 进行页面跳转，这是最常用的跳转方式。
              会在浏览器历史记录中添加新的条目，用户可以通过后退按钮返回。
            </p>
            <button @click="jumpWindowLocationHref" class="method-button primary">
              <SvgIcon name="play" size="18" />
              测试 location.href
            </button>
          </div>
        </div>

        <!-- window.open 跳转 -->
        <div class="method-card success">
          <div class="card-header">
            <div class="method-icon">
              <SvgIcon name="external-link" size="28" />
            </div>
            <div class="method-info">
              <h3 class="method-title">window.open</h3>
              <p class="method-desc">新窗口或标签页打开，不影响当前页面</p>
            </div>
            <div class="method-badge new-window">新窗口</div>
          </div>
          <div class="card-content">
            <p class="method-detail">
              使用 <code>window.open()</code> 在新窗口或新标签页中打开链接。
              不会影响当前页面，适合打开外部链接或需要保持当前页面状态的场景。
            </p>
            <button @click="jumpWindowOpen" class="method-button success">
              <SvgIcon name="play" size="18" />
              测试 window.open
            </button>
          </div>
        </div>

        <!-- location.replace 跳转 -->
        <div class="method-card warning">
          <div class="card-header">
            <div class="method-icon">
              <SvgIcon name="external-link" size="28" />
            </div>
            <div class="method-info">
              <h3 class="method-title">location.replace</h3>
              <p class="method-desc">替换当前页面，不会在历史记录中留下痕迹</p>
            </div>
            <div class="method-badge replace">替换</div>
          </div>
          <div class="card-content">
            <p class="method-detail">
              使用 <code>window.location.replace()</code> 替换当前页面。
              不会在浏览器历史记录中留下痕迹，用户无法通过后退按钮返回到当前页面。
            </p>
            <button @click="jumpWindowReplace" class="method-button warning">
              <SvgIcon name="play" size="18" />
              测试 location.replace
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const jumpWindowLocationHref = () => {
  console.log('使用 location.href 跳转')
  window.location.href = 'https://www.baidu.com'
}

const jumpWindowOpen = () => {
  console.log('使用 window.open 跳转')
  window.open('https://www.baidu.com', '_blank')
}

const jumpWindowReplace = () => {
  console.log('使用 location.replace 跳转')
  window.location.replace('https://www.baidu.com')
}
</script>

<style lang="scss" scoped>
.page-jump-page {
  @include page-container();
}

.page-container {
  @include content-container();
}

// 头部
.modern-header {
  text-align: center;
  margin-bottom: space(12);

  .header-content {
    .main-title {
      font-size: font(display);
      font-weight: weight(bold);
      color: color(text-primary);
      margin-bottom: space(4);
      background: linear-gradient(135deg, color(primary) 0%, color(info) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      font-size: font(body-lg);
      color: color(text-secondary);
      margin-bottom: space(6);
    }

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: space(2);
      padding: space(2) space(4);
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border-radius: radius(full);
      font-size: font(body-sm);
      color: color(text-secondary);
      border: 1px solid rgba(255, 255, 255, 0.2);

      svg {
        color: color(primary);
      }
    }
  }
}

// 方法网格
.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: space(8);
}

// 方法卡片
.method-card {
  @include glass-card();

  .card-header {
    display: flex;
    align-items: flex-start;
    gap: space(4);
    margin-bottom: space(6);
    padding-bottom: space(4);
    border-bottom: 1px solid color(border-light);

    .method-icon {
      width: 64px;
      height: 64px;
      border-radius: radius(xl);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      position: relative;

      svg {
        color: white;
        z-index: 2;
      }

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        z-index: 1;
      }
    }

    .method-info {
      flex: 1;

      .method-title {
        font-size: font(h3);
        font-weight: weight(semibold);
        color: color(text-primary);
        margin-bottom: space(2);
      }

      .method-desc {
        font-size: font(body-sm);
        color: color(text-secondary);
        line-height: 1.5;
      }
    }

    .method-badge {
      @include badge();
      align-self: flex-start;

      &.same-window {
        background: color(primary-100);
        color: color(primary-700);
      }

      &.new-window {
        background: color(success-100);
        color: color(success-700);
      }

      &.replace {
        background: color(warning-100);
        color: color(warning-600);
      }
    }
  }

  .card-content {
    .method-detail {
      font-size: font(body-sm);
      color: color(text-secondary);
      line-height: 1.6;
      margin-bottom: space(6);

      code {
        background: color(neutral-100);
        padding: space(1) space(2);
        border-radius: radius(sm);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: font(caption);
        color: color(primary);
      }
    }

    .method-button {
      @include button-primary();
      width: 100%;
      gap: space(2);

      &.success {
        background: gradient(success);
      }

      &.warning {
        background: gradient(warning);
      }
    }
  }

  // 不同颜色主题
  &.primary .method-icon::before {
    background: gradient(primary);
  }
  &.success .method-icon::before {
    background: gradient(success);
  }
  &.warning .method-icon::before {
    background: gradient(warning);
  }
}

// 响应式设计
@include responsive(lg) {
  .methods-grid {
    grid-template-columns: 1fr;
    gap: space(6);
  }
}

@include responsive(md) {
  .modern-container {
    padding: space(4) space(3);
  }

  .modern-header {
    margin-bottom: space(8);

    .header-content .main-title {
      font-size: font(h1);
    }
  }

  .methods-grid {
    grid-template-columns: 1fr;
    gap: space(4);
  }

  .method-card .card-header {
    flex-direction: column;
    align-items: center;
    text-align: center;

    .method-info {
      order: 2;
    }

    .method-badge {
      order: 3;
    }
  }
}
</style>
