<!--
 * @Author: ho<PERSON><PERSON><PERSON>
 * @Date: 2025-04-29 11:20:24
 * @Description: iOS 风格的页面跳转测试
 * @LastEditTime: 2025-06-05 14:01:57
 * @LastEditors: houbaoguo
-->
<template>
  <div class="ios-page-jump">
    <div class="ios-container">
      <!-- iOS 风格的导航栏 -->
      <div class="ios-header">
        <h1 class="ios-large-title">页面跳转测试</h1>
        <p class="ios-subtitle">测试不同的页面跳转方式</p>
      </div>

      <!-- iOS 风格的跳转方法列表 -->
      <div class="ios-section">
        <div class="ios-section-header">
          <h2 class="ios-section-title">跳转方法</h2>
        </div>
        <div class="ios-list-container">
          <!-- location.href 跳转 -->
          <div class="ios-method-card">
            <div class="ios-method-header">
              <div class="ios-method-info">
                <h3 class="ios-method-title">location.href</h3>
                <p class="ios-method-desc">当前窗口跳转，会在浏览器历史中留下记录</p>
              </div>
              <div class="ios-method-badge same-window">同窗口</div>
            </div>
            <button @click="jumpWindowLocationHref" class="ios-method-button system-blue">
              <SvgIcon name="external-link" size="18" />
              测试 location.href
            </button>
          </div>

          <!-- window.open 跳转 -->
          <div class="ios-method-card">
            <div class="ios-method-header">
              <div class="ios-method-info">
                <h3 class="ios-method-title">window.open</h3>
                <p class="ios-method-desc">新窗口或标签页打开，不影响当前页面</p>
              </div>
              <div class="ios-method-badge new-window">新窗口</div>
            </div>
            <button @click="jumpWindowOpen" class="ios-method-button system-green">
              <SvgIcon name="external-link" size="18" />
              测试 window.open
            </button>
          </div>

          <!-- location.replace 跳转 -->
          <div class="ios-method-card">
            <div class="ios-method-header">
              <div class="ios-method-info">
                <h3 class="ios-method-title">location.replace</h3>
                <p class="ios-method-desc">替换当前页面，不会在历史记录中留下痕迹</p>
              </div>
              <div class="ios-method-badge replace">替换</div>
            </div>
            <button @click="jumpWindowReplace" class="ios-method-button system-orange">
              <SvgIcon name="external-link" size="18" />
              测试 location.replace
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SvgIcon 组件会自动导入，无需手动引入

const jumpWindowLocationHref = () => {
  window.location.href = 'https://www.baidu.com'
}

const jumpWindowOpen = () => {
  window.open('https://www.baidu.com')
}
const jumpWindowReplace = () => {
  // window.location.replace('https://www.baidu.com')
  window.location.replace(
    'https://fund-test2.taixincf.com/ztoMiddleRedirectWhite?accessToken=xxx&authCode=xxxxx',
  )
}
</script>

<style lang="scss" scoped>
// iOS 风格的页面跳转测试
.ios-page-jump {
  @include ios-page-container();
}

.ios-container {
  max-width: 768px;
  margin: 0 auto;
  padding: 0 spacing(4);
}

// iOS 风格的头部
.ios-header {
  padding: spacing(8) 0 spacing(6);
  text-align: center;

  .ios-large-title {
    font-size: font-size(large-title);
    font-weight: font-weight(bold);
    color: color(label);
    margin-bottom: spacing(2);
    letter-spacing: -0.02em;
  }

  .ios-subtitle {
    font-size: font-size(subheadline);
    font-weight: font-weight(regular);
    color: color(secondary-label);
  }
}

// iOS 风格的分组
.ios-section {
  margin-bottom: spacing(8);

  .ios-section-header {
    padding: 0 spacing(4) spacing(2);

    .ios-section-title {
      font-size: font-size(footnote);
      font-weight: font-weight(regular);
      color: color(secondary-label);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

// iOS 风格的列表容器
.ios-list-container {
  background: color(secondary-system-grouped-background);
  border-radius: radius(base);
  overflow: hidden;
  box-shadow: shadow(ios-card);
  display: flex;
  flex-direction: column;
  gap: 1px;
  background: color(separator);
}
// iOS 风格的方法卡片
.ios-method-card {
  background: color(secondary-system-grouped-background);
  padding: spacing(4);
  transition: background-color 0.15s ease-out;

  &:active {
    background: color(tertiary-system-fill);
  }

  .ios-method-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: spacing(4);

    .ios-method-info {
      flex: 1;

      .ios-method-title {
        font-size: font-size(body);
        font-weight: font-weight(semibold);
        color: color(label);
        margin-bottom: spacing(1);
      }

      .ios-method-desc {
        font-size: font-size(footnote);
        font-weight: font-weight(regular);
        color: color(secondary-label);
        line-height: 1.4;
      }
    }

    .ios-method-badge {
      display: inline-flex;
      align-items: center;
      padding: spacing(1) spacing(2);
      border-radius: radius(sm);
      font-size: font-size(caption-1);
      font-weight: font-weight(medium);

      &.same-window {
        background: color-alpha(system-blue, 0.15);
        color: color(system-blue);
      }

      &.new-window {
        background: color-alpha(system-green, 0.15);
        color: color(system-green);
      }

      &.replace {
        background: color-alpha(system-orange, 0.15);
        color: color(system-orange);
      }
    }
  }

  .ios-method-button {
    @include ios-button-base();
    width: 100%;
    justify-content: center;
    gap: spacing(2);
    font-weight: font-weight(semibold);

    &.system-blue {
      background: color(system-blue);
      color: white;

      &:hover {
        background: color-mix(system-blue, #000000, 10%);
      }
    }

    &.system-green {
      background: color(system-green);
      color: white;

      &:hover {
        background: color-mix(system-green, #000000, 10%);
      }
    }

    &.system-orange {
      background: color(system-orange);
      color: white;

      &:hover {
        background: color-mix(system-orange, #000000, 10%);
      }
    }
  }
}
// 响应式适配
@include responsive(sm) {
  .ios-container {
    padding: 0 spacing(3);
  }

  .ios-header {
    padding: spacing(6) 0 spacing(4);

    .ios-large-title {
      font-size: font-size(title-1);
    }
  }
}
}
</style>
