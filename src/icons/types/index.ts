/**
 * 所有图标名称类型
 */
export type IconName =
  | 'home'
  | 'chevron-left'
  | 'arrow-left'
  | 'clipboard'
  | 'document'
  | 'shield'
  | 'code'
  | 'lightning'
  | 'external-link'
  | 'wechat'
  | 'credit-card'
  | 'play'
  | 'settings'
  | 'trash'

/**
 * 图标组件属性接口
 */
export interface IconProps {
  /** 图标名称 */
  name: IconName
  /** 图标颜色 */
  color?: string
  /** 图标大小 */
  size?: string | number
  /** 自定义类名 */
  className?: string
}

/**
 * 图标配置接口
 */
export interface IconConfig {
  /** 图标目录路径 */
  iconDirs: string[]
  /** Symbol ID 格式 */
  symbolId: string
  /** 注入位置 */
  inject: 'body-first' | 'body-last'
  /** 自定义 DOM ID */
  customDomId: string
}
