/**
 * SDK 调试模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

export const sdkRoute: RouteRecordRaw = {
  path: '/sdk',
  component: () => import('@/layout/Layout.vue'),
  children: [
    {
      path: 'methods',
      name: 'SdkMethods',
      component: () => import('@/views/sdk/JsBridgeMethods.vue'),
      meta: {
        title: 'SDK 方法调试',
        showBack: true,
        padding: false,
        backgroundColor: 'var(--color-ios-background)',
        group: 'sdk',
        description: '测试和调试各种SDK方法',
        icon: 'code',
      },
    },
    {
      path: 'protocol',
      name: 'SdkProtocol',
      component: () => import('@/views/sdk/ProtocolTest.vue'),
      meta: {
        title: '协议测试',
        showBack: true,
        group: 'sdk',
        description: '测试协议接口和通信',
        icon: 'network',
      },
    },
  ],
}
