/**
 * 开发测试模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

export const devRoute: RouteRecordRaw = {
  path: '/dev',
  component: () => import('@/layout/Layout.vue'),
  children: [
    {
      path: 'test-one',
      name: 'DevTestOne',
      component: () => import('@/views/dev/TestOne.vue'),
      meta: {
        title: '开发测试页面 1',
        showBack: true,
        group: 'dev',
        description: '开发和调试用的测试页面',
        icon: 'test',
      },
    },
    {
      path: 'test-two',
      name: 'DevTestTwo',
      component: () => import('@/views/dev/TestTwo.vue'),
      meta: {
        title: '开发测试页面 2',
        showBack: true,
        group: 'dev',
        description: '开发和调试用的测试页面',
        icon: 'test',
      },
    },
  ],
}
