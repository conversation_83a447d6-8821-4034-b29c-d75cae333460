/**
 * 第三方集成模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

export const integrationsRoute: RouteRecordRaw = {
  path: '/integrations',
  component: () => import('@/layout/Layout.vue'),
  children: [
    {
      path: 'wechat-miniprogram',
      name: 'WechatMiniProgram',
      component: () => import('@/views/integrations/LaunchWechatMiniProgram.vue'),
      meta: {
        title: '微信小程序启动',
        showBack: true,
        group: 'integrations',
        description: '测试微信小程序启动功能',
        icon: 'wechat',
      },
    },
    {
      path: 'mep-bridge',
      name: 'MepBridge',
      component: () => import('@/views/integrations/MepBridge.vue'),
      meta: {
        title: 'MEP Bridge 测试',
        showBack: true,
        group: 'integrations',
        description: '测试MEP桥接功能',
        icon: 'bridge',
      },
    },
  ],
}
