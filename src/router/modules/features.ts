/**
 * 功能测试模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

export const featuresRoute: RouteRecordRaw = {
  path: '/features',
  component: () => import('@/layout/Layout.vue'),
  children: [
    {
      path: 'web-capacity',
      name: 'WebCapacity',
      component: () => import('@/views/features/WebCapacityLimitModern.vue'),
      meta: {
        title: 'Web能力限制',
        showBack: true,
        group: 'features',
        description: '现代化的Web API访问限制检测',
        icon: 'shield',
      },
    },
    {
      path: 'page-jump',
      name: 'PageJump',
      component: () => import('@/views/features/PageJumpModern.vue'),
      meta: {
        title: '页面跳转测试',
        showBack: true,
        group: 'features',
        description: '现代化的页面跳转测试',
        icon: 'link',
      },
    },
  ],
}
