/*
 * @Author: houbaog<PERSON>
 * @Date: 2024-12-17 09:04:24
 * @Description:
 * @LastEditTime: 2025-04-29 11:20:32
 * @LastEditors: houbaoguo
 */
import {
  createRouter,
  createWebHistory,
  // createMemoryHistory,
  // createWebHashHistory,
} from 'vue-router'

// 导入分类路由模块
import { homeRoute } from '@/router/modules/home'
import { sdkRoute } from '@/router/modules/sdk'
import { featuresRoute } from '@/router/modules/features'
import { integrationsRoute } from '@/router/modules/integrations'
import { devRoute } from '@/router/modules/dev'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  // history: createWebHashHistory(),
  routes: [
    // 1. 首页
    homeRoute,

    // 2. SDK 调试模块
    sdkRoute,

    // 3. 功能测试模块
    featuresRoute,

    // 4. 第三方集成模块
    integrationsRoute,

    // 5. 开发测试模块
    devRoute,
  ],
})

export default router
