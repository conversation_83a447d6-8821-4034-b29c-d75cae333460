<script setup lang="ts">
// App.vue 只负责渲染第一层路由，不直接导入布局组件

const initFxLifeJS = () => {
  if (window?.fx) {
    handleFxLifeJSReady()
  } else {
    document.addEventListener('fxLifeJSReady', handleFxLifeJSReady)
  }
}
// 监听 fxLifeJSReady 事件的处理函数
const handleFxLifeJSReady = () => {
  console.log('FxLifeJS is ready')
  window.fx?.hideProgramLoading()
  /* window.fx?.init({
    appId: '123',
    debug: true,
    jsApiList: [],
    success: () => {
      console.log('init success')
    },
    fail: (err) => {
      console.log('init fail', err)
    },
    complete: () => {
      console.log('init complete')
    },
  }) */
}

onMounted(() => {
  console.log('start onMounted')
  initFxLifeJS()
})

onUnmounted(() => {
  console.log('start onUnmounted')
  // 移除事件监听
  document.removeEventListener('fxLifeJSReady', handleFxLifeJSReady)
})
</script>

<template>
  <!-- 第一层 RouterView - 渲染主布局或直接页面 -->
  <RouterView />
</template>

<style scoped></style>
