<template>
  <div class="app-layout">
    <!-- 导航栏 -->
    <AppNavbar
      v-if="shouldShowNavbar"
      :title="pageTitle"
      :subtitle="pageSubtitle"
      :show-back="shouldShowBack"
      :back-text="backText"
      :background-color="navbarBackground"
      :transparent="navbarTransparent"
      @back="handleBack"
    >
      <!-- 导航栏插槽 -->
      <template #left>
        <slot name="navbar-left" />
      </template>

      <template #title>
        <slot name="navbar-title">{{ pageTitle }}</slot>
      </template>

      <template #right>
        <slot name="navbar-right" />
      </template>
    </AppNavbar>

    <!-- 子路由视图 -->
    <main class="app-main" :class="mainClasses">
      <RouterView v-slot="{ Component, route }">
        <Transition name="slide-fade" mode="out-in">
          <component :is="Component" :key="route.path" />
        </Transition>
      </RouterView>
    </main>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script setup lang="ts">
import AppNavbar from '@/layout/AppNavbar.vue'

// 获取当前路由信息
const route = useRoute()
const router = useRouter()

// 从路由元信息中获取页面配置
const pageTitle = computed(() => {
  return (route.meta.title as string) || '页面'
})

const pageSubtitle = computed(() => {
  return (route.meta.subtitle as string) || ''
})

const shouldShowNavbar = computed(() => {
  // 默认显示导航栏，除非路由元信息中明确设置为false
  return route.meta.showNavbar !== false
})

const shouldShowBack = computed(() => {
  // 首页不显示返回按钮，其他页面默认显示
  if (route.path === '/') return false
  return route.meta.showBack !== false
})

const backText = computed(() => {
  return (route.meta.backText as string) || ''
})

const navbarBackground = computed(() => {
  return (route.meta.navbarBackground as string) || ''
})

const navbarTransparent = computed(() => {
  return (route.meta.navbarTransparent as boolean) || false
})

const pageBackground = computed(() => {
  return (route.meta.backgroundColor as string) || 'var(--color-ios-background)'
})

const padding = computed(() => {
  return route.meta.padding !== false
})

const scrollable = computed(() => {
  return route.meta.scrollable !== false
})

// 计算主要内容区域类名
const mainClasses = computed(() => ({
  'app-main--padding': padding.value,
  'app-main--scrollable': scrollable.value,
  'app-main--no-scroll': !scrollable.value,
}))

// 处理返回事件
const handleBack = () => {
  // 检查路由元信息中是否有自定义返回处理
  if (route.meta.onBack && typeof route.meta.onBack === 'function') {
    route.meta.onBack()
    return
  }

  // 默认返回逻辑
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}

// 设置页面标题
watchEffect(() => {
  if (pageTitle.value) {
    document.title = pageTitle.value
  }
})
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  min-height: -webkit-fill-available;
  background-color: v-bind('pageBackground');
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

.app-main--padding {
  padding: var(--spacing-4);
}

.app-main--scrollable {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.app-main--no-scroll {
  overflow: hidden;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .app-main--padding {
    padding: var(--spacing-3);
  }
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* iOS 安全区域适配 */
@supports (padding: max(0px)) {
  .app-layout {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }
}
</style>
