<template>
  <header class="app-navbar">
    <!-- 安全区域适配 -->
    <div class="safe-area-top"></div>

    <!-- 导航栏内容 -->
    <div class="navbar-content">
      <!-- 左侧区域 -->
      <div class="navbar-left">
        <button
          v-if="showBack"
          @click="handleBack"
          class="back-button"
          :class="{ 'back-button--disabled': backDisabled }"
          :disabled="backDisabled"
        >
          <SvgIcon name="chevron-left" size="24" />
          <span v-if="backText" class="back-text">{{ backText }}</span>
        </button>

        <!-- 左侧自定义内容插槽 -->
        <slot name="left" />
      </div>

      <!-- 中间标题区域 -->
      <div class="navbar-center">
        <h1 class="navbar-title">
          <slot name="title">{{ title }}</slot>
        </h1>
        <p v-if="subtitle" class="navbar-subtitle">{{ subtitle }}</p>
      </div>

      <!-- 右侧区域 -->
      <div class="navbar-right">
        <!-- 右侧自定义内容插槽 -->
        <slot name="right" />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
// SvgIcon 组件会自动导入，无需手动引入

interface Props {
  /** 页面标题 */
  title?: string
  /** 副标题 */
  subtitle?: string
  /** 是否显示返回按钮 */
  showBack?: boolean
  /** 返回按钮文字 */
  backText?: string
  /** 返回按钮是否禁用 */
  backDisabled?: boolean
  /** 导航栏背景色 */
  backgroundColor?: string
  /** 是否透明背景 */
  transparent?: boolean
}

interface Emits {
  /** 点击返回按钮事件 */
  (e: 'back'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  showBack: true,
  backText: '',
  backDisabled: false,
  backgroundColor: '',
  transparent: false,
})

const emit = defineEmits<Emits>()

// 处理返回按钮点击
const handleBack = () => {
  if (props.backDisabled) return

  emit('back')

  // 如果没有监听back事件，默认使用路由返回
  const instance = getCurrentInstance()
  if (!instance?.vnode.props?.onBack) {
    const router = useRouter()
    // 检查是否可以返回
    if (window.history.length > 1) {
      router.back()
    } else {
      // 如果没有历史记录，跳转到首页
      router.push('/')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: v-bind(
    'props.transparent ? "transparent" : (props.backgroundColor || "rgba(255, 255, 255, 0.8)")'
  );
  @include navbar();
}

.app-navbar.transparent {
  background-color: transparent;
  border-bottom-color: transparent;
}

/*
 * 使用 CSS Grid 实现完美的三栏布局
 * - 左栏：返回按钮和左侧内容 (1fr)
 * - 中栏：标题内容 (auto，但居中对齐)
 * - 右栏：右侧内容 (1fr)
 * 这样确保标题始终在视觉中心，不受左右内容影响
 */
.navbar-content {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  height: 44px;
  padding: 0 space(4);
  max-width: 100%;
  gap: space(2);
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-self: start;
  min-width: 0;
}

.navbar-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  justify-self: center;
  min-width: 0;
  text-align: center;
  /* 确保标题区域不会超出可用空间 */
  max-width: 100%;
  overflow: hidden;
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-self: end;
  min-width: 0;
}

.back-button {
  display: flex;
  align-items: center;
  gap: space(1);
  padding: space(2);
  margin-left: calc(-1 * #{space(2)});
  color: color(primary);
  background: none;
  border: none;
  cursor: pointer;
  border-radius: radius(sm);
  transition: transition(fast);
  min-height: 44px;
  min-width: 44px;
  -webkit-tap-highlight-color: transparent;
}

.back-button:active {
  opacity: 0.3;
  transform: scale(0.95);
}

.back-button--disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.back-button--disabled:active {
  transform: none;
}

.back-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.back-text {
  font-size: font(body);
  font-weight: weight(normal);
  white-space: nowrap;
}

.navbar-title {
  font-size: font(h3);
  font-weight: weight(semibold);
  color: color(text-primary);
  line-height: 1.2;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  /* 确保标题在 Grid 布局中正确显示 */
  min-width: 0;
}

.navbar-subtitle {
  font-size: font(caption);
  color: color(text-secondary);
  line-height: 1;
  margin: 0;
  margin-top: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 响应式适配 */
@include responsive(sm) {
  .navbar-content {
    padding: 0 space(3);
    gap: space(1);
  }

  .back-text {
    display: none;
  }

  .navbar-title {
    font-size: font(body);
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .app-navbar {
    background-color: v-bind(
      'props.transparent ? "transparent" : (props.backgroundColor || "rgba(28, 28, 30, 0.94)")'
    );
  }
}
</style>
