/**
 * 路由元信息类型定义
 */

declare module 'vue-router' {
  interface RouteMeta {
    /** 页面标题 */
    title?: string
    /** 页面副标题 */
    subtitle?: string
    /** 是否显示导航栏 */
    showNavbar?: boolean
    /** 是否显示返回按钮 */
    showBack?: boolean
    /** 返回按钮文字 */
    backText?: string
    /** 导航栏背景色 */
    navbarBackground?: string
    /** 导航栏是否透明 */
    navbarTransparent?: boolean
    /** 页面背景色 */
    backgroundColor?: string
    /** 内容区域是否有内边距 */
    padding?: boolean
    /** 是否全屏显示 */
    fullscreen?: boolean
    /** 内容区域是否可滚动 */
    scrollable?: boolean
    /** 自定义返回处理函数 */
    onBack?: () => void | Promise<void>
    /** 页面权限 */
    requiresAuth?: boolean
    /** 页面角色权限 */
    roles?: string[]
    /** 页面描述 */
    description?: string
    /** 页面关键词 */
    keywords?: string[]
    /** 是否缓存页面 */
    keepAlive?: boolean
    /** 路由分组 */
    group?: 'sdk' | 'features' | 'integrations' | 'dev'
    /** 页面图标 */
    icon?: string
    /** 是否在导航中显示 */
    showInNav?: boolean
  }
}

export {}
