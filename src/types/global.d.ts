/* eslint-disable @typescript-eslint/no-explicit-any */
interface callback {
  success?: (res: any) => void
  fail?: (err: any) => void
  complete?: () => void
}
interface JarvisSDK {
  invoke: (method: string, params: any & callback) => void
  init: (
    options: {
      appId: string
      debug: boolean
      jsApiList: string[]
    } & callback,
  ) => void
  requestPayment: (params: any & callback) => void
  hideProgramLoading: () => void
  chooseImage: (params: any & callback) => void
  chooseVideo: (params: any & callback) => void
  chooseFile: (params: any & callback) => void
  exit: (params: any & callback) => void
}

interface Window {
  fx?: JarvisSDK
  LaunchWechatMiniProgram?: any
}
