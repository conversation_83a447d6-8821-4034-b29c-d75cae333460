/**
 * 页面布局组合式函数
 * 提供页面布局相关的状态和方法
 */

export interface PageLayoutOptions {
  /** 页面标题 */
  title?: string
  /** 副标题 */
  subtitle?: string
  /** 是否显示导航栏 */
  showNavbar?: boolean
  /** 是否显示返回按钮 */
  showBack?: boolean
  /** 返回按钮文字 */
  backText?: string
  /** 导航栏背景色 */
  navbarBackground?: string
  /** 导航栏是否透明 */
  navbarTransparent?: boolean
  /** 页面背景色 */
  backgroundColor?: string
  /** 内容区域是否有内边距 */
  padding?: boolean
  /** 是否全屏显示 */
  fullscreen?: boolean
  /** 内容区域是否可滚动 */
  scrollable?: boolean
  /** 自定义返回处理函数 */
  onBack?: () => void | Promise<void>
}

export function usePageLayout(options: PageLayoutOptions = {}) {
  // 响应式状态
  const layoutState = reactive({
    title: options.title || '',
    subtitle: options.subtitle || '',
    showNavbar: options.showNavbar ?? true,
    showBack: options.showBack ?? true,
    backText: options.backText || '',
    navbarBackground: options.navbarBackground || '',
    navbarTransparent: options.navbarTransparent ?? false,
    backgroundColor: options.backgroundColor || '',
    padding: options.padding ?? true,
    fullscreen: options.fullscreen ?? false,
    scrollable: options.scrollable ?? true,
    loading: false,
    loadingText: '加载中...',
    backDisabled: false,
  })

  // 设置页面标题
  const setTitle = (title: string, subtitle?: string) => {
    layoutState.title = title
    if (subtitle !== undefined) {
      layoutState.subtitle = subtitle
    }
    // 同时设置浏览器标题
    document.title = title
  }

  // 设置加载状态
  const setLoading = (loading: boolean, text?: string) => {
    layoutState.loading = loading
    if (text) {
      layoutState.loadingText = text
    }
  }

  // 设置导航栏样式
  const setNavbarStyle = (style: {
    background?: string
    transparent?: boolean
  }) => {
    if (style.background !== undefined) {
      layoutState.navbarBackground = style.background
    }
    if (style.transparent !== undefined) {
      layoutState.navbarTransparent = style.transparent
    }
  }

  // 设置返回按钮状态
  const setBackButton = (config: {
    show?: boolean
    text?: string
    disabled?: boolean
  }) => {
    if (config.show !== undefined) {
      layoutState.showBack = config.show
    }
    if (config.text !== undefined) {
      layoutState.backText = config.text
    }
    if (config.disabled !== undefined) {
      layoutState.backDisabled = config.disabled
    }
  }

  // 处理返回操作
  const handleBack = async () => {
    if (layoutState.backDisabled) return

    try {
      // 如果有自定义返回处理函数，优先执行
      if (options.onBack) {
        await options.onBack()
        return
      }

      // 默认返回逻辑
      const router = useRouter()
      
      // 检查是否可以返回
      if (window.history.length > 1) {
        router.back()
      } else {
        // 如果没有历史记录，跳转到首页
        router.push('/')
      }
    } catch (error) {
      console.error('返回操作失败:', error)
    }
  }

  // 切换全屏模式
  const toggleFullscreen = (fullscreen?: boolean) => {
    layoutState.fullscreen = fullscreen ?? !layoutState.fullscreen
  }

  // 设置页面背景
  const setBackground = (color: string) => {
    layoutState.backgroundColor = color
  }

  // 设置内容区域样式
  const setContentStyle = (style: {
    padding?: boolean
    scrollable?: boolean
  }) => {
    if (style.padding !== undefined) {
      layoutState.padding = style.padding
    }
    if (style.scrollable !== undefined) {
      layoutState.scrollable = style.scrollable
    }
  }

  // 显示加载提示
  const showLoading = (text?: string) => {
    setLoading(true, text)
  }

  // 隐藏加载提示
  const hideLoading = () => {
    setLoading(false)
  }

  // 页面进入时的初始化
  onMounted(() => {
    if (layoutState.title) {
      document.title = layoutState.title
    }
  })

  // 页面离开时的清理
  onUnmounted(() => {
    // 清理定时器等资源
  })

  return {
    // 状态
    layoutState: readonly(layoutState),
    
    // 方法
    setTitle,
    setLoading,
    setNavbarStyle,
    setBackButton,
    handleBack,
    toggleFullscreen,
    setBackground,
    setContentStyle,
    showLoading,
    hideLoading,
  }
}

/**
 * 页面元数据配置
 */
export interface PageMeta {
  title?: string
  subtitle?: string
  showNavbar?: boolean
  showBack?: boolean
  backText?: string
  fullscreen?: boolean
  backgroundColor?: string
  navbarTransparent?: boolean
}

/**
 * 从路由元信息中获取页面配置
 */
export function usePageMeta(): PageMeta {
  const route = useRoute()
  
  return {
    title: route.meta.title as string,
    subtitle: route.meta.subtitle as string,
    showNavbar: route.meta.showNavbar as boolean,
    showBack: route.meta.showBack as boolean,
    backText: route.meta.backText as string,
    fullscreen: route.meta.fullscreen as boolean,
    backgroundColor: route.meta.backgroundColor as string,
    navbarTransparent: route.meta.navbarTransparent as boolean,
  }
}
