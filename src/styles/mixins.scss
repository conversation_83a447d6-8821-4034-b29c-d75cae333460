// 现代化设计系统混合
@use 'sass:map';
@use './variables.scss' as *;

// Apple 设计系统函数
@function color($name) {
  @return map.get($modern-colors, $name);
}

@function font($name) {
  @return map.get($modern-fonts, $name);
}

@function weight($name) {
  @return map.get($modern-weights, $name);
}

@function space($name) {
  @return map.get($modern-spacing, $name);
}

@function radius($name) {
  @return map.get($modern-radius, $name);
}

@function shadow($name) {
  @return map.get($modern-shadows, $name);
}

@function gradient($name) {
  @return map.get($modern-gradients, $name);
}

@function transition($name) {
  @return map.get($modern-transitions, $name);
}

// 响应式断点混合
@mixin responsive($breakpoint) {
  @media (max-width: map.get($modern-breakpoints, $breakpoint)) {
    @content;
  }
}

// ===== 统一页面容器系统 =====

// 基础页面容器
@mixin page-container() {
  min-height: 100vh;
  background: color(bg-secondary);
  font-family:
    -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: color(text-primary);
  line-height: 1.47; // Apple 标准行高
}

// 现代化风格页面容器
@mixin modern-page-container() {
  @include page-container();
  background: linear-gradient(135deg, color(bg-secondary) 0%, color(gray-50) 100%);
}

// iOS风格页面容器
@mixin ios-page-container() {
  @include page-container();
  background: color(system-grouped-background);
}

// 统一内容容器
@mixin content-container($max-width: 1200px) {
  max-width: $max-width;
  margin: 0 auto;
  padding: space(6) space(4);

  @include responsive(md) {
    padding: space(4) space(3);
  }

  @include responsive(sm) {
    padding: space(3) space(2);
  }
}

// iOS风格内容容器
@mixin ios-content-container($max-width: 768px) {
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 space(4);

  @include responsive(sm) {
    padding: 0 space(3);
  }
}

// ===== 统一卡片系统 =====

// 基础卡片样式
@mixin card($padding: 4, $radius: lg) {
  background: color(surface-primary);
  border-radius: radius($radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 0.5px solid color(border-normal);
  padding: space($padding);
  transition: all 0.25s ease-out;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

// 现代化卡片样式
@mixin modern-card($padding: 4, $radius: lg) {
  @include card($padding, $radius);
  background: color(surface-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

// iOS风格卡片样式
@mixin ios-card($padding: 4, $radius: base) {
  background: color(secondary-system-grouped-background);
  border-radius: radius($radius);
  box-shadow: shadow(ios-card);
  padding: space($padding);
  transition: all 0.25s ease-out;

  &:hover {
    background: color(tertiary-system-grouped-background);
  }
}

// ===== 统一页面头部系统 =====

// 现代化页面头部
@mixin modern-page-header() {
  text-align: center;
  margin-bottom: space(8);
  padding: space(8) 0;

  .main-title {
    font-size: font(h1);
    font-weight: weight(bold);
    color: color(text-primary);
    margin-bottom: space(2);
    letter-spacing: -0.02em;
  }

  .subtitle {
    font-size: font(subheadline);
    color: color(text-secondary);
    line-height: 1.4;
  }

  @include responsive(md) {
    margin-bottom: space(6);
    padding: space(6) 0;

    .main-title {
      font-size: font(h2);
    }
  }
}

// iOS风格页面头部
@mixin ios-page-header() {
  padding: space(8) 0 space(6);
  text-align: center;

  .ios-large-title {
    font-size: font(largeTitle);
    font-weight: weight(bold);
    color: color(label);
    margin-bottom: space(2);
    letter-spacing: -0.02em;
  }

  .ios-subtitle {
    font-size: font(subheadline);
    font-weight: weight(regular);
    color: color(secondary-label);
  }

  @include responsive(sm) {
    padding: space(6) 0 space(4);

    .ios-large-title {
      font-size: font(title1);
    }
  }
}

// 玻璃效果卡片
@mixin glass-card($padding: 6, $radius: lg) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: radius($radius);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: space($padding);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: transition(normal);

  &:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-4px);
    box-shadow:
      0 16px 48px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }
}

// Apple 风格按钮基础样式
@mixin button-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: space(3) space(4);
  border-radius: radius(lg);
  font-size: font(body);
  font-weight: weight(medium);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-out;
  text-decoration: none;
  min-height: 44px;
  position: relative;
  overflow: hidden;
  font-family: inherit;

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none !important;
  }

  &:active:not(:disabled) {
    transform: scale(0.96);
  }

  // Apple 风格的焦点环
  &:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
  }
}

// Apple 风格主要按钮
@mixin button-primary() {
  @include button-base();
  background: color(primary);
  color: color(text-inverse);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background: color(primary-600);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    background: color(primary-700);
    transform: scale(0.96);
  }
}

// 次要按钮
@mixin button-secondary() {
  @include button-base();
  background: color(surface-primary);
  color: color(primary);
  border: 1px solid color(border-normal);

  &:hover:not(:disabled) {
    background: color(primary-50);
    border-color: color(primary);
    box-shadow: shadow(sm);
  }
}

// 幽灵按钮
@mixin button-ghost() {
  @include button-base();
  background: transparent;
  color: color(primary);

  &:hover:not(:disabled) {
    background: color(primary-50);
  }
}

// Apple 风格输入框
@mixin input() {
  width: 100%;
  padding: space(3) space(4);
  background: color(surface-primary);
  border: 0.5px solid color(border-normal);
  border-radius: radius(lg);
  font-size: font(body);
  font-family: inherit;
  color: color(text-primary);
  transition: all 0.2s ease-out;
  min-height: 44px;

  &:focus {
    outline: none;
    border-color: color(primary);
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
  }

  &::placeholder {
    color: color(text-tertiary);
  }

  &:disabled {
    background: color(gray-100);
    color: color(text-tertiary);
    cursor: not-allowed;
  }
}

// 标签
@mixin badge($color: primary) {
  display: inline-flex;
  align-items: center;
  padding: space(1) space(3);
  border-radius: radius(full);
  font-size: font(caption);
  font-weight: weight(medium);
  background: color(#{$color}-100);
  color: color(#{$color}-700);
}

// 分隔线
@mixin divider() {
  height: 1px;
  background: color(border-normal);
  border: none;
  margin: space(4) 0;
}

// 加载动画关键帧（需要在全局作用域）
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 加载动画
@mixin loading-spinner($size: 20px, $color: primary) {
  width: $size;
  height: $size;
  border: 2px solid color(border-light);
  border-top: 2px solid color($color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 文本截断
@mixin truncate() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本截断
@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Apple 风格的焦点环
@mixin focus-ring($color: primary) {
  &:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
  }
}

// Apple 风格的导航栏
@mixin navbar() {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 0.5px solid color(border-normal);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-out;
}
