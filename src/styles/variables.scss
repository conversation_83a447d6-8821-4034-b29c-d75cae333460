// Apple Human Interface Guidelines 设计系统 - 使用 CSS 变量
$modern-colors: (
  // Apple 系统颜色
  primary: var(--color-primary),
  primary-50: var(--color-primary-50),
  primary-100: var(--color-primary-100),
  primary-200: var(--color-primary-200),
  primary-300: var(--color-primary-300),
  primary-400: var(--color-primary-400),
  primary-500: var(--color-primary-500),
  primary-600: var(--color-primary-600),
  primary-700: var(--color-primary-700),
  primary-800: var(--color-primary-800),
  primary-900: var(--color-primary-900),
  // Apple 中性色系
  gray-50: var(--color-gray-50),
  gray-100: var(--color-gray-100),
  gray-200: var(--color-gray-200),
  gray-300: var(--color-gray-300),
  gray-400: var(--color-gray-400),
  gray-500: var(--color-gray-500),
  gray-600: var(--color-gray-600),
  gray-700: var(--color-gray-700),
  gray-800: var(--color-gray-800),
  gray-900: var(--color-gray-900),
  // Apple 系统颜色
  success: var(--color-success),
  success-50: var(--color-success-50),
  success-100: var(--color-success-100),
  success-500: var(--color-success-500),
  success-600: var(--color-success-600),
  success-700: var(--color-success-700),
  warning: var(--color-warning),
  warning-50: var(--color-warning-50),
  warning-100: var(--color-warning-100),
  warning-500: var(--color-warning-500),
  warning-600: var(--color-warning-600),
  error: var(--color-error),
  error-50: var(--color-error-50),
  error-100: var(--color-error-100),
  error-500: var(--color-error-500),
  error-600: var(--color-error-600),
  info: var(--color-info),
  info-50: var(--color-info-50),
  info-100: var(--color-info-100),
  info-500: var(--color-info-500),
  info-600: var(--color-info-600),
  // Apple 紫色系
  purple: var(--color-purple),
  purple-50: var(--color-purple-50),
  purple-100: var(--color-purple-100),
  purple-500: var(--color-purple-500),
  purple-600: var(--color-purple-600),
  // Apple 语义化颜色
  text-primary: var(--color-text-primary),
  text-secondary: var(--color-text-secondary),
  text-tertiary: var(--color-text-tertiary),
  text-inverse: var(--color-text-inverse),
  // Apple 背景色
  bg-primary: var(--color-bg-primary),
  bg-secondary: var(--color-bg-secondary),
  bg-tertiary: var(--color-bg-tertiary),
  // Apple 边框色
  border-light: var(--color-border-light),
  border-normal: var(--color-border-normal),
  border-dark: var(--color-border-dark),
  // Apple 表面色
  surface-primary: var(--color-surface-primary),
  surface-secondary: #fafafa,
  surface-elevated: #ffffff,

  // 覆盖层
  overlay: rgba(0, 0, 0, 0.5),
  overlay-light: rgba(0, 0, 0, 0.1)
);

// Apple 字体系统 - 基于 SF Pro
$modern-fonts: (
  // Apple 字体层级
  largeTitle: 2.125rem,
  // 34px - Large Title
  title1: 1.75rem,
  // 28px - Title 1
  title2: 1.375rem,
  // 22px - Title 2
  title3: 1.25rem,
  // 20px - Title 3
  headline: 1.0625rem,
  // 17px - Headline
  body: 1.0625rem,
  // 17px - Body
  callout: 1rem,
  // 16px - Callout
  subheadline: 0.9375rem,
  // 15px - Subheadline
  footnote: 0.8125rem,
  // 13px - Footnote
  caption1: 0.75rem,
  // 12px - Caption 1
  caption2: 0.6875rem,

  // 11px - Caption 2
  // 兼容性别名
  display: 2.125rem,
  h1: 1.75rem,
  h2: 1.375rem,
  h3: 1.25rem,
  h4: 1.0625rem,
  body-lg: 1.0625rem,
  body-sm: 0.9375rem,
  caption: 0.8125rem
);

// Apple 字体权重
$modern-weights: (
  ultraLight: 100,
  thin: 200,
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  heavy: 800,
  black: 900,

  // 兼容性别名
  normal: 400,
  extrabold: 800,
);

// 现代化间距系统
$modern-spacing: (
  0: 0,
  1: 0.25rem,
  // 4px
  2: 0.5rem,
  // 8px
  3: 0.75rem,
  // 12px
  4: 1rem,
  // 16px
  5: 1.25rem,
  // 20px
  6: 1.5rem,
  // 24px
  8: 2rem,
  // 32px
  10: 2.5rem,
  // 40px
  12: 3rem,
  // 48px
  16: 4rem,
  // 64px
  20: 5rem,
  // 80px
  24: 6rem, // 96px
);

// Apple 圆角系统 - 更大的圆角
$modern-radius: (
  none: 0,
  xs: 0.125rem,
  // 2px
  sm: 0.25rem,
  // 4px
  md: 0.5rem,
  // 8px - Apple 标准圆角
  lg: 0.75rem,
  // 12px - Apple 大圆角
  xl: 1rem,
  // 16px - Apple 超大圆角
  2xl: 1.25rem,
  // 20px
  3xl: 1.5rem,
  // 24px
  full: 9999px,
);

// 现代化阴影系统
$modern-shadows: (
  sm: (
    0 1px 2px 0 rgba(0, 0, 0, 0.05),
  ),
  base: (
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06),
  ),
  md: (
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
  ),
  lg: (
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
  ),
  xl: (
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
  ),
  2xl: (
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
  ),
  inner: (
    inset 0 2px 4px 0 rgba(0, 0, 0, 0.06),
  ),
);

// 现代化渐变
$modern-gradients: (
  primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%),
  success: linear-gradient(135deg, #22c55e 0%, #15803d 100%),
  warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%),
  error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%),
  purple: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%),
  glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
);

// 现代化断点
$modern-breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px,
);

// 现代化过渡动画
$modern-transitions: (
  fast: 150ms ease-out,
  normal: 250ms ease-out,
  slow: 350ms ease-out,
  bounce: 250ms cubic-bezier(0.68, -0.55, 0.265, 1.55),
);

// 现代化 Z-index
$modern-z-index: (
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal-backdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
);
