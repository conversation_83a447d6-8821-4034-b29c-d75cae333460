/* Apple Human Interface Guidelines CSS 变量 - 唯一数据源 */
:root {
  /* Apple 系统颜色 */
  --color-primary: #007AFF;
  --color-primary-50: #F0F8FF;
  --color-primary-100: #E6F3FF;
  --color-primary-200: #CCE7FF;
  --color-primary-300: #99D1FF;
  --color-primary-400: #66BBFF;
  --color-primary-500: #007AFF;
  --color-primary-600: #0056CC;
  --color-primary-700: #003D99;
  --color-primary-800: #002966;
  --color-primary-900: #001A33;

  --color-success: #34C759;
  --color-success-50: #F0FFF4;
  --color-success-100: #E6FFE6;
  --color-success-500: #34C759;
  --color-success-600: #28A745;
  --color-success-700: #1E7E34;

  --color-warning: #FF9500;
  --color-warning-50: #FFF8F0;
  --color-warning-100: #FFEDE0;
  --color-warning-500: #FF9500;
  --color-warning-600: #E6850E;

  --color-error: #FF3B30;
  --color-error-50: #FFF5F5;
  --color-error-100: #FFE6E6;
  --color-error-500: #FF3B30;
  --color-error-600: #E6342A;

  --color-info: #5AC8FA;
  --color-info-50: #F0FCFF;
  --color-info-100: #E6F9FF;
  --color-info-500: #5AC8FA;
  --color-info-600: #32ADE6;

  --color-purple: #AF52DE;
  --color-purple-50: #FAF5FF;
  --color-purple-100: #F3E8FF;
  --color-purple-500: #AF52DE;
  --color-purple-600: #9333EA;

  /* Apple 中性色系 */
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F5F5F7;
  --color-gray-200: #E5E5EA;
  --color-gray-300: #D2D2D7;
  --color-gray-400: #AEAEB2;
  --color-gray-500: #8E8E93;
  --color-gray-600: #636366;
  --color-gray-700: #48484A;
  --color-gray-800: #2C2C2E;
  --color-gray-900: #1C1C1E;

  /* Apple 语义化颜色 */
  --color-text-primary: #1C1C1E;
  --color-text-secondary: #8E8E93;
  --color-text-tertiary: #AEAEB2;
  --color-text-inverse: #FFFFFF;

  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F2F2F7;
  --color-bg-tertiary: #FFFFFF;

  --color-surface-primary: #FFFFFF;
  --color-surface-secondary: #F2F2F7;

  --color-border-light: #F2F2F7;
  --color-border-normal: #E5E5EA;
  --color-border-dark: #D2D2D7;

  /* Apple 字体大小 */
  --font-size-largeTitle: 2.125rem;    /* 34px */
  --font-size-title1: 1.75rem;         /* 28px */
  --font-size-title2: 1.375rem;        /* 22px */
  --font-size-title3: 1.25rem;         /* 20px */
  --font-size-headline: 1.0625rem;     /* 17px */
  --font-size-body: 1.0625rem;         /* 17px */
  --font-size-callout: 1rem;           /* 16px */
  --font-size-subheadline: 0.9375rem;  /* 15px */
  --font-size-footnote: 0.8125rem;     /* 13px */
  --font-size-caption1: 0.75rem;       /* 12px */
  --font-size-caption2: 0.6875rem;     /* 11px */

  /* Apple 字体权重 */
  --font-weight-ultraLight: 100;
  --font-weight-thin: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-heavy: 800;
  --font-weight-black: 900;

  /* Apple 间距系统 - 8pt 网格 */
  --spacing-0: 0;
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */

  /* Apple 圆角系统 */
  --radius-xs: 0.125rem;     /* 2px */
  --radius-sm: 0.25rem;      /* 4px */
  --radius-md: 0.5rem;       /* 8px */
  --radius-lg: 0.75rem;      /* 12px */
  --radius-xl: 1rem;         /* 16px */
  --radius-2xl: 1.25rem;     /* 20px */
  --radius-3xl: 1.5rem;      /* 24px */
  --radius-full: 9999px;

  /* Apple 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-card: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-modal: 0 10px 40px rgba(0, 0, 0, 0.2);

  /* Apple 过渡动画 */
  --transition-fast: 0.15s;
  --transition-normal: 0.25s;
  --transition-slow: 0.35s;
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Apple 渐变 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-600) 100%);
  --gradient-warning: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-600) 100%);
  --gradient-error: linear-gradient(135deg, var(--color-error) 0%, var(--color-error-600) 100%);
  --gradient-purple: linear-gradient(135deg, var(--color-purple) 0%, var(--color-purple-600) 100%);
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text-primary: #FFFFFF;
    --color-text-secondary: #AEAEB2;
    --color-text-tertiary: #636366;
    --color-text-inverse: #1C1C1E;

    --color-bg-primary: #000000;
    --color-bg-secondary: #1C1C1E;
    --color-bg-tertiary: #2C2C2E;

    --color-surface-primary: #1C1C1E;
    --color-surface-secondary: #2C2C2E;

    --color-border-light: #2C2C2E;
    --color-border-normal: #48484A;
    --color-border-dark: #636366;
  }
}
