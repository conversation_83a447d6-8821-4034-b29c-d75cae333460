/**
 * 样式入口文件
 * 统一导入所有样式文件
 */

/* 导入全局样式（包含重置样式） */
@import './reset.css';
@import './global.css';

/* 
 * 可以在这里导入其他样式文件
 * 例如：
 * @import './components.css';
 * @import './utilities.css';
 * @import './animations.css';
 */

/* ===== 项目特定的全局样式 ===== */

/* 确保应用占满整个视口 */
#app {
  min-height: 100vh;
  min-height: -webkit-fill-available;
}

/* iOS 特定的样式优化 */
@supports (-webkit-touch-callout: none) {
  #app {
    min-height: -webkit-fill-available;
  }
}

/* 防止移动端双击缩放 */
* {
  touch-action: manipulation;
}

/* 优化移动端滚动 */
body {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
}

/* 确保所有交互元素都有足够的触摸目标大小 */
@media (hover: none) and (pointer: coarse) {
  button,
  a,
  input,
  textarea,
  select,
  [role='button'],
  [tabindex] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 隐藏元素但保持可访问性 */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 跳转到主内容的链接（可访问性） */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #3b82f6;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 0.375rem;
  z-index: 1070;
  transition: top 150ms cubic-bezier(0, 0, 0.2, 1);
}

.skip-to-content:focus {
  top: 6px;
}

/* 打印样式 */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: ' (' attr(href) ')';
  }

  abbr[title]::after {
    content: ' (' attr(title) ')';
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
