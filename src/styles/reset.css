/**
 *  CSS 重置样式
 * 基于 modern-normalize 和最佳实践
 */

/* ===== 基础重置 ===== */

/* 使用更好的盒模型 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 移除默认边距和内边距 */
* {
  margin: 0;
  padding: 0;
}

/* 设置根元素 */
html {
  /* 防止iOS中方向改变时的字体大小调整 */
  -webkit-text-size-adjust: 100%;
  /* 设置更好的字体渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 设置默认字体大小 */
  font-size: 16px;
  /* 设置行高 */
  line-height: 1.5;
  /* 设置滚动行为 */
  scroll-behavior: smooth;
}

/* 设置body */
body {
  /* 设置默认字体栈 */
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  /* 设置默认字体大小 */
  font-size: 1rem;
  /* 设置默认行高 */
  line-height: 1.5;
  /* 设置默认颜色 */
  color: #333;
  /* 设置默认背景色 */
  background-color: #fff;
  /* 防止水平滚动 */
  overflow-x: hidden;
  /* 设置最小高度 */
  min-height: 100vh;
  /* 设置文本渲染优化 */
  text-rendering: optimizeSpeed;
}

/* ===== 标题重置 ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
}

/* ===== 列表重置 ===== */
ul,
ol {
  list-style: none;
}

/* ===== 链接重置 ===== */
a {
  color: inherit;
  text-decoration: none;
  /* 移除点击时的高亮 */
  -webkit-tap-highlight-color: transparent;
}

/* ===== 按钮重置 ===== */
button {
  background: none;
  border: none;
  font: inherit;
  cursor: pointer;
  /* 移除点击时的高亮 */
  -webkit-tap-highlight-color: transparent;
  /* 移除默认的按钮样式 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* ===== 表单元素重置 ===== */
input,
textarea,
select {
  font: inherit;
  color: inherit;
  background: none;
  border: none;
  outline: none;
  /* 移除默认样式 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 移除 number 输入框的箭头 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

/* ===== 图片和媒体重置 ===== */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ===== 表格重置 ===== */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* ===== 其他元素重置 ===== */
hr {
  border: none;
  height: 1px;
  background-color: #e5e5e5;
}

/* 移除 fieldset 的默认样式 */
fieldset {
  border: none;
}

/* 移除 legend 的默认样式 */
legend {
  padding: 0;
}

/* ===== 可访问性改进 ===== */
:focus {
  outline: none;
}

/* 隐藏元素但保持可访问性 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== 移动端优化 ===== */

/* 改善移动端触摸体验 */
@media (hover: none) and (pointer: coarse) {
  button,
  a,
  input,
  textarea,
  select {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 防止移动端横屏时字体缩放 */
@media screen and (orientation: landscape) {
  html {
    -webkit-text-size-adjust: 100%;
  }
}
