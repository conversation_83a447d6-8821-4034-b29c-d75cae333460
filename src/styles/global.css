/**
 * 全局样式
 * 只保留UnoCSS无法覆盖的部分和项目特有的设计系统
 * 大部分样式已迁移到 UnoCSS 和 CSS 变量系统
 */

/* ===== 基础样式（使用我们的 Apple 设计系统） ===== */

html {
  font-family:
    -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-body);
  line-height: 1.47; /* Apple 标准行高 */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  font-weight: var(--font-weight-regular);
  line-height: 1.47;
}

/* ===== 项目特有的样式（UnoCSS 无法完全覆盖的部分） ===== */

/* Apple 风格的毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 0.5px solid rgba(255, 255, 255, 0.3);
}

/* Apple 风格的按钮点击效果 */
.apple-button-effect {
  transition: all 0.2s ease-out;
}

.apple-button-effect:active {
  transform: scale(0.96);
}

/* Apple 风格的焦点环 */
.apple-focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
}

/* ===== 特殊组件样式（无法用 UnoCSS 完全替代的部分） ===== */

/* Apple 风格的分段控制器 */
.segmented-control {
  display: flex;
  background-color: var(--color-gray-100);
  border-radius: var(--radius-lg);
  padding: 2px;
}

.segmented-control-item {
  flex: 1;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-subheadline);
  font-weight: var(--font-weight-medium);
  text-align: center;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal) var(--ease-spring);
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  color: var(--color-text-secondary);
}

.segmented-control-item.active {
  background-color: var(--color-surface-primary);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-card);
}

/* ===== 安全区域适配 ===== */

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* ===== 滚动条样式 ===== */

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* iOS 风格滚动条 */
::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 1.5px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* ===== 特殊效果和优化 ===== */

/* 移动端触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .apple-button-effect {
    -webkit-tap-highlight-color: transparent;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .glass-effect {
    border-width: 0.5px;
  }
}
