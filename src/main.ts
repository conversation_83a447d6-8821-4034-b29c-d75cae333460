// 导入 CSS 变量 - 唯一数据源
import './styles/css-variables.css'
// 导入全局样式（包含重置样式、变量和工具类）
import './styles/index.css'
// 导入 UnoCSS 样式
import 'virtual:uno.css'
// 导入 SVG 图标
import 'virtual:svg-icons-register'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { initVConsole } from './utils/debug'

// 初始化 VConsole
initVConsole()

// 监控白屏时间和 FCP
if (window.PerformanceObserver) {
  const observer = new PerformanceObserver((entryList) => {
    const entries = entryList.getEntries()
    entries.forEach((entry) => {
      if (entry.name === 'first-paint') {
        console.log(`白屏时间: ${entry.startTime}ms`)
      }
      if (entry.name === 'first-contentful-paint') {
        console.log(`首个内容绘制时间 (FCP): ${entry.startTime}ms`)
      }
    })
    observer.disconnect()
  })
  observer.observe({ type: 'paint', buffered: true })
}

const app = createApp(App)

// 在首屏路由加载时采集首屏渲染完成时间
router.beforeEach((to, from, next) => {
  if (from.path === '/' && to.path === '/') {
    window.performance.mark('navigation-start')
  }
  next()
})

router.afterEach(() => {
  if (window.IntersectionObserver) {
    const firstScreenElements = document.querySelectorAll('#app')
    const intersectionObserver = new IntersectionObserver((entries, observer) => {
      let allVisible = true
      entries.forEach((entry) => {
        if (!entry.isIntersecting) allVisible = false
      })

      if (allVisible) {
        console.log(`首屏渲染完成时间: ${performance.now()}ms`)
        observer.disconnect()
      }
    })

    firstScreenElements.forEach((el) => intersectionObserver.observe(el))
  }
})

app.use(router)

app.mount('#app')
