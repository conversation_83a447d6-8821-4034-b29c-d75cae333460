import type { Theme } from 'vitepress'
import DefaultTheme from 'vitepress/theme'
import InputParams from '../../components/input-params.vue'
import OutputParams from '../../components/output-params.vue'
import './style.css'

export default {
  extends: DefaultTheme,
  enhanceApp({ app }) {
    // 注册自定义全局组件
    app.component('InputParams', InputParams)
    app.component('OutputParams', OutputParams)
  },
} satisfies Theme
