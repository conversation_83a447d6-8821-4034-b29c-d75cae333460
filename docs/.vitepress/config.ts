/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 10:33:49
 * @Description:
 * @LastEditTime: 2025-03-14 14:52:49
 * @LastEditors: houbaoguo
 */
import { type DefaultTheme, defineConfig } from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: '纷享生活JS-SDK',
  description: '基于纷享生活 App 内网页应用的开发工具包',
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: navConfig(),

    sidebar: {
      '/guide/': {
        base: '/guide/',
        items: sidebarGuide(),
      },
      '/sdk-methods/': {
        base: '/sdk-methods/',
        items: sidebarSdkMethods(),
      },
    },

    docFooter: {
      prev: '上一页',
      next: '下一页',
    },

    outline: {
      label: '页面导航',
    },
    lastUpdated: {
      text: '最后更新于',
      formatOptions: {
        dateStyle: 'short',
        timeStyle: 'medium',
      },
    },
    returnToTopLabel: '回到顶部',
    sidebarMenuLabel: '菜单',
    darkModeSwitchLabel: '主题',
    lightModeSwitchTitle: '切换到浅色模式',
    darkModeSwitchTitle: '切换到深色模式',
    skipToContentLabel: '跳转到内容',
    search: {
      provider: 'local',
    },
  },
})

// nav 配置
function navConfig(): DefaultTheme.NavItem[] {
  return [
    { text: '指南', link: '/guide/what-is-life-js-sdk', activeMatch: '/guide' },
    { text: 'sdk方法', link: '/sdk-methods/attention', activeMatch: '/sdk-methods' },
  ]
}

// sidebar 配置
// 指南
function sidebarGuide(): DefaultTheme.SidebarItem[] {
  return [
    {
      text: '指南',
      collapsed: false,
      items: [
        { text: '介绍', link: 'what-is-life-js-sdk' },
        {
          text: '起步',
          link: 'application-info',
        },
        {
          text: '场景值',
          link: 'scene-value',
        },
      ],
    },
  ]
}

// sdk方法
function sidebarSdkMethods(): DefaultTheme.SidebarItem[] {
  return [
    {
      text: '用前必读',
      items: [
        { text: '注意事项', link: 'attention' },
        { text: '通用参数', link: 'common-params' },
        { text: 'code码列表', link: 'code-list' },
      ],
    },
    {
      text: '开放方法',
      items: [
        { text: '基础能力', link: 'm-can-i-use' },
        { text: '登录', link: 'm-login' },
        { text: '支付', link: 'm-request-payment' },
        { text: '扫一扫', link: 'm-scan-code' },
        { text: '获取应用当前版本号', link: 'm-get-app-version' },
        { text: '更新APP', link: 'm-update-app' },
        { text: '获取当前设备信息', link: 'm-get-device-info' },
        { text: '获取状态栏高度', link: 'm-get-status-bar-height' },
        { text: '获取胶囊的位置信息', link: 'm-get-capsule-position' },
        { text: '隐藏loading', link: 'm-hide-program-loading' },
        { text: '设置是否全屏', link: 'm-set-full-screen' },
        { text: '显示原生弹窗', link: 'm-show-modal' },
        { text: '打开其他应用', link: 'm-open-mini-program' },
        { text: '打开纷享生活客服', link: 'm-open-customer-service-chat' },
        { text: '获取纷享生活用户手机号', link: 'm-get-phone-number' },
        { text: '获取生活用户实名信息', link: 'm-get-real-user-info' },
        { text: '获取昵称头像', link: 'm-get-user-profile' },
        { text: '打开原生地址选择', link: 'm-choose-address' },
        { text: '获取默认地址', link: 'm-get-default-address' },
        { text: '选择文件', link: 'm-choose-file' },
        { text: '获取经纬度信息', link: 'm-get-location-info' },
        {
          text: '分享内容至外部应用',
          link: 'm-share-message-to-external-app',
        },
        { text: '获取底部安全区域高度', link: 'm-get-bottom-safe-height' },
        { text: '设置状态栏样式', link: 'm-set-status-bar-theme' },
        { text: '退出登录并打开登录页面', link: 'm-go-login' },
        { text: '退出应用', link: 'm-exit' },
      ],
    },
  ]
}
