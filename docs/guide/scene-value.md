<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 13:43:50
 * @Description:
 * @LastEditTime: 2025-03-14 13:57:28
 * @LastEditors: houbaoguo
-->

# 场景值说明

## 场景值定义

场景值用来描述纷享生活用户进入应用的路径,完整的场景值的含义见下场景值列表。

## 获取方式

开发者可通过以下方式获取场景值：

当应用在纷享生活APP内被打开时，原始链接会自动添加 `_scene` 参数，应用侧应解析URL中的 `_scene` 参数值获取当前场景值

## 注意事项

参数覆盖规则：

- 原始链接：`https://abcd.com/home`
- 实际打开链接：`https://abcd.com/home?_scene=1000`

- 原始链接含场景参数：`https://abcd.com/home?_scene=ztolife`
- 实际打开链接：`https://abcd.com/home?_scene=1000`（原参数会被覆盖）

::: warning 注意

请避免在应用首页链接中主动携带 `_scene` 参数，原始链接中存在 `_scene` 参数打开时<b style="color: red;">会被覆盖</b>

:::

## 场景值对照表

| 场景值 | 场景说明                                                                                         |
| ------ | ------------------------------------------------------------------------------------------------ |
| 1000   | 纷享生活 APP 内部打开应用                                                                        |
| 1001   | Schemes 协议跳转, 其他 APP 通过纷享生活 APP 提供的 Schemes 协议来打开某款应用的场景              |
| 1002   | **仅 iOS 支持, universal link 的形式唤起应用**                                                   |
| 1003   | 纷享生活 APP 扫码打开应用                                                                        |
| 1004   | 其他应用跳转,应用被其他应用通过 [`openMiniProgram`](/sdk-methods/m-open-mini-program) 的形式打开 |
| 1005   | 纷享生活 APP 推送消息跳转                                                                        |
