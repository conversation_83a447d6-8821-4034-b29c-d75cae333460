<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 11:51:39
 * @Description:
 * @LastEditTime: 2025-03-14 13:41:58
 * @LastEditors: houbaoguo
-->

# 应用信息指南

::: warning 注意

应用服务提供方需完整提供以下信息，否则将受到能力限制或使用禁止

:::

## 一、基本信息

### 1.1 基础信息

| 信息项       | 释义       | 是否必须 | 备注/注意事项                                                                     |
| ------------ | ---------- | -------- | --------------------------------------------------------------------------------- |
| 应用名称     | H5应用名称 | 是       | - 2-6字精炼名称<br>- 禁用"demo/test/测试"等字眼<br>- 需与简介内容对应             |
| 应用logo     | H5应用标识 | 是       | - 正方形高清图(≤50KB)<br>- 背景不透明<br>- 核心内容居中<br>- 禁止使用中通相关标识 |
| 应用简介     | 业务介绍   | 否       | 10-100字简明说明，需与业务内容一致                                                |
| 应用标签     | -          | -        | -                                                                                 |
| 应用服务类目 | 服务分类   | 是       | 类目不符将导致审核不通过                                                          |
| 企业名称     | -          | 是       | -                                                                                 |
| 企业简称     | -          | 是       | -                                                                                 |
| 营业执照号   | -          | 是       | -                                                                                 |

### 1.2 客服信息

| 信息项   | 释义         | 是否必须 | 备注         |
| -------- | ------------ | -------- | ------------ |
| 客服电话 | 官方联系方式 | 否       | 固话需含区号 |
| 客服时间 | 服务时段     | 否       | -            |

## 二、合作资质

| 资质项       | 说明         | 备注                                                                                     |
| ------------ | ------------ | ---------------------------------------------------------------------------------------- |
| 合作确认函   | 合作协议文件 | -                                                                                        |
| 合作到期时间 | 合作终止日期 | 非必要字段,若存在合作到期时间,须在到期时间前进行续约,否则应用会在纷享生活 App 内无法访问 |

## 三、服务配置

### 3.1 网络配置

| 配置项   | 说明                                                     | 是否必须 | 备注 |
| -------- | -------------------------------------------------------- | -------- | ---- |
| 应用域名 | H5访问地址                                               | 是       |      |
| IP白名单 | 通过服务端接口形式来获取纷享生活用户敏感信息时访问来源ip | 是       | -    |

### 3.2 安全凭证

> **纷享生活提供**以下数据,**开发时所需字段**,如下表

| 凭证类型     | 用途             | 注意事项                                                              |
| ------------ | ---------------- | --------------------------------------------------------------------- |
| appId        | 应用识别码       | 配合其他参数调用生活接口能力关键参数                                  |
| appSecret    | 应用身份验证密钥 | <b style="color: red;">请勿将秘钥存于前端业务代码中或者交于第三方</b> |
| 加解密密钥对 | 公钥私钥密钥对   | 用于信息传输过程中进行加/解密                                         |

## 四、接口权限申请

- **需在完成前三章配置后申请**
- sdk能力详见[SDK方法合集](/sdk-methods/attention.html)
- 若有需要可提供相关场景示例进行权限申请
