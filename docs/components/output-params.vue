<template>
  <div class="output-params">
    <table>
      <thead>
        <tr>
          <th>参数名称</th>
          <th>类型</th>
          <th>说明</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in renderData" :key="item.name">
          <td>{{ item.name }}</td>
          <td>{{ item.type }}</td>
          <td>{{ item.description }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = withDefaults(
  defineProps<{
    showBaseData?: boolean;
    data?: {
      name: string;
      type: string;
      description: string;
    }[];
  }>(),
  {
    showBaseData: false,
    data: () => [],
  }
);

const baseData = [
  {
    name: "code",
    type: "Number",
    description: "状态码，成功为 0，失败为 -1",
  },
  {
    name: "bizCode",
    type: "String",
    description: "特定业务错误码",
  },
  {
    name: "msg",
    type: "String",
    description: "操作结果描述，成功返回'调用成功'，失败返回具体错误原因",
  },
  {
    name: "data",
    type: "Object",
    description:
      "返回数据对象，成功时包含接口具体数据，无特殊说明时返回null，失败时返回null",
  },
];

const renderData = computed(() => {
  if (props.showBaseData) {
    return [...props.data, ...baseData];
  }
  return props.data;
});
</script>

<style scoped>
.output-params {
  width: 100%;
  margin: 1rem 0;
  overflow-x: auto;
}

table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin: 1rem 0;
  --vp-c-text-2: #000;
}

th,
td {
  padding: 0.75rem;
  border: 1px solid var(--vp-c-divider);
  text-align: left;
  word-break: break-word;
}

th:nth-child(1),
td:nth-child(1) {
  width: 20%;
}
th:nth-child(2),
td:nth-child(2) {
  width: 20%;
}
th:nth-child(3),
td:nth-child(3) {
  width: 60%;
}

th {
  background-color: var(--vp-c-bg-soft);
  font-weight: 600;
}

tr:nth-child(even) {
  background-color: var(--vp-c-bg-soft);
}

@media (max-width: 640px) {
  th,
  td {
    padding: 0.5rem;
  }
}
</style>
