<template>
  <div class="input-params" :class="{ isShowRequired: needRequired }">
    <table>
      <thead>
        <tr>
          <th>参数名称</th>
          <th>类型</th>
          <th v-if="needRequired">必填</th>
          <th>说明</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in renderData" :key="item.name">
          <td>{{ item.name }}</td>
          <td>{{ item.type }}</td>
          <td v-if="needRequired">{{ item.required ? "是" : "否" }}</td>
          <td v-html="item.description"></td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = withDefaults(
  defineProps<{
    needRequired?: boolean;
    data?: {
      name: string;
      type: string;
      required: boolean;
      description: string;
    }[];
  }>(),
  {
    needRequired: true,
    data: () => [],
  }
);

const baseData = [
  {
    name: "timeout",
    type: "Number",
    required: false,
    description: "接口调用超时时间，单位为毫秒",
  },
  {
    name: "success",
    type: "Function",
    required: false,
    description: "接口调用成功的回调函数",
  },
  {
    name: "fail",
    type: "Function",
    required: false,
    description: "接口调用失败的回调函数",
  },
  {
    name: "complete",
    type: "Function",
    required: false,
    description: "接口调用结束的回调函数（调用成功、失败都会执行）",
  },
];
const renderData = computed(() => {
  return [...props.data, ...baseData];
});
</script>

<style scoped>
.input-params {
  width: 100%;
  margin: 1rem 0;
  overflow-x: auto;
}

table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin: 1rem 0;
  --vp-c-text-2: #000;
}

th,
td {
  padding: 0.75rem;
  border: 1px solid var(--vp-c-divider);
  text-align: left;
  word-break: break-word;
}
th:nth-child(1),
td:nth-child(1) {
  width: 20%;
}
th:nth-child(2),
td:nth-child(2) {
  width: 20%;
}
th:nth-child(3),
td:nth-child(3) {
  width: 60%;
}

.isShowRequired th:nth-child(1),
.isShowRequired td:nth-child(1) {
  width: 20%;
}
.isShowRequired th:nth-child(2),
.isShowRequired td:nth-child(2) {
  width: 15%;
}
.isShowRequired th:nth-child(3),
.isShowRequired td:nth-child(3) {
  width: 10%;
}
.isShowRequired th:nth-child(4),
.isShowRequired td:nth-child(4) {
  width: 55%;
}

th {
  background-color: var(--vp-c-bg-soft);
  font-weight: 600;
}

tr:nth-child(even) {
  background-color: var(--vp-c-bg-soft);
}

@media (max-width: 640px) {
  th,
  td {
    padding: 0.5rem;
  }
}
</style>
