<!--
 * @Author: houbaoguo
 * @Date: 2025-07-28 11:45:23
 * @Description:
 * @LastEditTime: 2025-07-28 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'needAuthConfirm',
    type: 'Boolean',
    required: false,
    description: '是否每次都显示授权弹框',
  },
]

const outputParams = [
  {
    name: 'addressInfo',
    type: 'String',
    description: '地址信息JSON字符串',
  },
]
</script>

# chooseAddress

::: warning 版本支持情况

- iOS: 1.1.46 开始支持
- Android: 1.0.40 开始支持

  :::

## 功能描述

打开原生地址选择器

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.chooseAddress({
  needAuthConfirm: true,
  success(res) {
    // res.data.addressInfo 地址信息JSON字符串
    const addressInfo = JSON.parse(res.data.addressInfo);
    console.log('选择的地址信息:', addressInfo);
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
