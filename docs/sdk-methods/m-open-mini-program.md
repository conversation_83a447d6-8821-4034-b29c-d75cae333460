<!--
 * @Author: houba<PERSON><PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-21 15:20:01
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'appId',
    type: 'String',
    required: true,
    description: '要打开的应用 appId',
  },
  {
    name: 'type',
    type: 'String',
    required: false,
    description: '路由跳转方式，枚举：navigateTo ｜ redirectTo 缺省时默认navigateTo',
  },
  {
    name: 'extraData',
    type: 'String',
    required: false,
    description: `需要传递给目标应用的数据，会在链接上额外拼接，不支持复杂数据eg: "{'a':1,'b':2}"`,
  },
]
</script>

# openMiniProgram

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

打开纷享生活 APP 内其他应用，被打开应用需是正常上架状态

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: 通用参数出参，无特殊

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.openMiniProgram({
  appId: 'abcd12345',
  extraData: { a: 1 }, //值优先级最高，会替换 path 链接后拼接a，此时携带a 的值为 1；若是 path 值为空，应用首页存在同样参数也会被替换为 1
  success(res) {
    // 成功回调
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
