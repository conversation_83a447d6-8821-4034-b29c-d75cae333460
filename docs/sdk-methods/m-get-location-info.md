<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-07-28 11:45:23
 * @Description:
 * @LastEditTime: 2025-07-28 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const outputParams = [
  {
    name: 'locationInfo',
    type: 'String',
    description: '位置信息JSON字符串，包含经纬度、区域、详细地址等信息',
  },
]
</script>

# getLocationInfo

::: warning 版本支持情况

- iOS: 1.1.48 开始支持
- Android: 1.0.41 开始支持

  :::

## 功能描述

获取当前位置的经纬度信息

## 参数

<InputParams />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.getLocationInfo({
  success(res) {
    // res.data.locationInfo 位置信息JSON字符串
    const locationInfo = JSON.parse(res.data.locationInfo);
    console.log('经度:', locationInfo.longitude);
    console.log('纬度:', locationInfo.latitude);
    console.log('区域:', locationInfo.area);
    console.log('详细地址:', locationInfo.address);
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```

## 返回数据示例

```json
{
  "locationInfo": "{\"longitude\":121.50050961839464,\"area\":\"上海-上海市-虹口区\",\"address\":\"上海市虹口区瑞虹路181号瑞虹天地太阳宫L1-7(商场1楼外围沿街商铺近天虹路)\",\"latitude\":31.262208070997559}"
}
```

解析后的locationInfo对象包含：
- `longitude`: 经度
- `latitude`: 纬度  
- `area`: 区域信息
- `address`: 详细地址
