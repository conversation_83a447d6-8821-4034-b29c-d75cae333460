<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'onlyFromCamera',
    type: 'Boolean',
    required: false,
    description: '是否只能从相机扫码，不允许从相册选择图片，默认false',
  },
  {
    name: 'scanType',
    type: 'Array',
    required: false,
    description: '扫码类型，barCode:一维码，qrCode:二维码，datamatrix:Data Matrix 码，pdf417:PDF417 条码，默认 ["barCode","qrCode"]',
  },
]

const outputParams = [
  {
    name: 'result',
    type: 'String',
    description: '所扫码的内容',
  },
]
</script>

# scanCode

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

扫一扫功能，可扫描二维码、条形码等

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.scanCode({
  scanType: ['barCode', 'qrCode'],
  success(res) {
    //res.data.result 当前结果
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
