<!--
 * @Author: houbaoguo
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'type',
    type: 'Number',
    required: true,
    description: '0(图文)、1(纯文字)、2(纯图片)、3(音乐)、4(视频)、5(小程序)',
  },
  {
    name: 'scene',
    type: 'String',
    required: true,
    description: '分享场景：WXSceneSession(分享到聊天界面)、WXSceneTimeline(分享到朋友圈)、WXSceneFavorite(分享到微信收藏)',
  },
  {
    name: 'title',
    type: 'String',
    required: false,
    description: '分享内容的标题',
  },
  {
    name: 'desc',
    type: 'String',
    required: false,
    description: '分享描述',
  },
  {
    name: 'summary',
    type: 'String',
    required: false,
    description: '跳转摘要，当type为1(纯文字)时必选',
  },
  {
    name: 'href',
    type: 'String',
    required: false,
    description: '跳转链接',
  },
  {
    name: 'imageUrl',
    type: 'String',
    required: false,
    description: '分享图片地址',
  },
  {
    name: 'mediaUrl',
    type: 'String',
    required: false,
    description: '音视频地址，当type为3(音乐)或4(视频)时必填',
  },
  {
    name: 'miniProgram',
    type: 'Object',
    required: false,
    description: '小程序信息，当type为5(小程序)时必填，包含id(微信小程序原始id)、path(进入的页面)、type(小程序版本类型：0-正式版、1-测试版、2-体验版，默认为0)',
  },
]
</script>

# shareMessageToExternalApp

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

分享内容至外部应用

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: 通用参数出参，无特殊

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.shareMessageToExternalApp({
  type: 0,
  scene: 'WXSceneSession',
  title: '分享标题',
  desc: '分享描述',
  summary: '跳转摘要',
  href: 'https://example.com',
  imageUrl: 'https://example.com/image.jpg',
  success(res) {
    // 成功回调
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
