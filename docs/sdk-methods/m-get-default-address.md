<!--
 * @Author: houba<PERSON><PERSON>
 * @Date: 2025-07-28 11:45:23
 * @Description:
 * @LastEditTime: 2025-07-28 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const outputParams = [
  {
    name: 'addressInfo',
    type: 'String',
    description: '默认地址信息JSON字符串',
  },
]
</script>

# getDefaultAddress

::: warning 版本支持情况

- iOS: 1.1.48 开始支持
- Android: 1.0.41 开始支持

  :::

## 功能描述

获取用户默认地址信息

## 参数

<InputParams />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.getDefaultAddress({
  success(res) {
    // res.data.addressInfo 默认地址信息JSON字符串
    const addressInfo = JSON.parse(res.data.addressInfo);
    console.log('默认地址信息:', addressInfo);
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
