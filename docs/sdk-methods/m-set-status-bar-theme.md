<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'mode',
    type: 'String',
    required: true,
    description: '状态栏主题：light、dark',
  },
]
</script>

# setStatusBarTheme

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

设置状态栏样式

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: 通用参数出参，无特殊

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.setStatusBarTheme({
  mode: 'light',
  success(res) {
    // 成功回调
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
