<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const outputParams = [
  {
    name: 'deviceId',
    type: 'String',
    description: '设备id',
  },
  {
    name: 'osName',
    type: 'String',
    description: '系统名称',
  },
  {
    name: 'osVersion',
    type: 'String',
    description: '系统版本',
  },
]
</script>

# getDeviceInfo

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

获取当前设备信息

## 参数

<InputParams />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.getDeviceInfo({
  success(res) {
    // res.data.deviceId 设备id
    // res.data.osName 系统名称
    // res.data.osVersion 系统版本
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
