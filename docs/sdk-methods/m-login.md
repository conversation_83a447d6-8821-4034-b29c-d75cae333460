<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const outputParams = [
  {
    name: 'code',
    type: 'String',
    description: '用户登录凭证(存在有效期)，应用开发者需要使用 code 换取 openid 等信息',
  },
]
</script>

# login

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

获取纷享生活下发的临时登录凭证 code，通过 code 获取纷享生活提供的用户身份标识，快速建立应用的用户体系，之后应用开发者服务器可以根据用户标识来生成自定义登录态，用于后续业务逻辑中前后端交互识别用户身份。

<!-- 图片 -->
<img src="/assets/images/login.png" alt="login" />

## 参数

<InputParams />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.login({
  success(res) {
    //res.data.code 当前结果
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
