<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-07-28 11:45:23
 * @Description:
 * @LastEditTime: 2025-07-28 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'fileType',
    type: 'String',
    required: false,
    description: '文件类型，不传则选择所有文件类型',
  },
]

const outputParams = [
  {
    name: 'url',
    type: 'String',
    description: '选择的文件地址',
  },
]
</script>

# chooseFile

::: warning 版本支持情况

- iOS: 1.1.48 开始支持
- Android: 1.0.41 开始支持

  :::

## 功能描述

选择文件，返回文件地址

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.chooseFile({
  fileType: 'image', // 可选，指定文件类型
  success(res) {
    // res.data.url 选择的文件地址
    console.log('选择的文件地址:', res.data.url);
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})

// 选择所有文件类型
fx.chooseFile({
  success(res) {
    // res.data.url 选择的文件地址
    console.log('选择的文件地址:', res.data.url);
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
