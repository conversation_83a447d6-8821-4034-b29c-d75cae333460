<!--
 * @Author: ho<PERSON><PERSON><PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 17:56:24
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'params',
    type: 'String',
    required: true,
    description:
      '使用 ${method}.${param}.${option} 方式来调用<br> ${method} 代表调用方法<br> ${param} 代表入参(request_param)或者返回值(response_param),其他值无效<br> ${option} 代表入参某个参数的可选值或者返回值某个属性',
  },
]

const outputParams = [
  {
    name: 'data',
    type: 'Boolean',
    description: 'true表示可用，false表示不可用',
  },
]
</script>

# canIUse

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

:::

## 功能描述

判断 JS-SDK 的 API 在当前纷享生活的版本是否可用

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
//是否可用 login
fx.canIUse({
  params: 'login',
  success: (res) => {
    // res.data === true 表示可正常用
  },
  fail: (res) => {
    // 可通过返回参数来确定失败原因
  },
})
```
