<!--
 * @Author: ho<PERSON><PERSON><PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const outputParams = [
  {
    name: 'left',
    type: 'Number',
    description: '距离屏幕左边距离',
  },
  {
    name: 'top',
    type: 'Number',
    description: '距离屏幕上边距离',
  },
  {
    name: 'width',
    type: 'Number',
    description: '自身宽度',
  },
  {
    name: 'height',
    type: 'Number',
    description: '自身高度',
  },
]
</script>

# getCapsulePosition

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

获取胶囊的位置信息

## 参数

<InputParams />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.getCapsulePosition({
  success(res) {
    // res.data.left 距离屏幕左边距离
    // res.data.top 距离屏幕上边距离
    // res.data.width 自身宽度
    // res.data.height 自身高度
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
