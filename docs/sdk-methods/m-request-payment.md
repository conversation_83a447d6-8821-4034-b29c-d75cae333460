<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const inputParams = [
  {
    name: 'orderNo',
    type: 'String',
    required: true,
    description: '订单号',
  },
  {
    name: 'bizType',
    type: 'String',
    required: true,
    description: '业务类型',
  },
]
</script>

# requestPayment

- iOS: 1.1.36 开始支持，≥ 1.22.3
- Android: 1.0.33 开始支持，≥ 1.22.3

## 功能描述

根据订单号发起支付拉收银台

## 参数

<InputParams :data="inputParams" />

### object.success 回调函数

参数: 通用参数出参，无特殊

### object.fail 回调函数

参数: 通用参数出参，无特殊

### 异常bizCode

- 20030001: 支付失败
- 20030002: 收银台拉起失败

## 示例代码

```javascript
fx.requestPayment({
  orderNo: '1212',
  bizType: 'yourBizType',
  success(res) {
    // 成功回调
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
