<!--
 * @Author: houba<PERSON><PERSON>
 * @Date: 2025-03-14 11:45:23
 * @Description:
 * @LastEditTime: 2025-03-14 14:46:03
 * @LastEditors: houbaoguo
-->

<script setup lang="ts">
const outputParams = [
  {
    name: 'userName',
    type: 'String',
    description: '密文用户真实姓名',
  },
  {
    name: 'idcard',
    type: 'String',
    description: '密文用户身份证号',
  },
]
</script>

# getRealUserInfo

::: warning 版本支持情况

- iOS: 1.1.36 开始支持
- Android: 1.0.33 开始支持

  :::

## 功能描述

获取生活用户实名信息

## 参数

<InputParams />

### object.success 回调函数

参数: Object object.data

<OutputParams :data="outputParams" />

### object.fail 回调函数

参数: 通用参数出参，无特殊

## 示例代码

```javascript
fx.getRealUserInfo({
  success(res) {
    // res.data.userName 密文用户真实姓名
    // res.data.idcard 密文用户身份证号
  },
  fail(res) {
    //res.msg 错误信息
    //res.code 错误码
  },
})
```
