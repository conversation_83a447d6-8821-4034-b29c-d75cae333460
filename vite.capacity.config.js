import { defineConfig } from 'vite'
import { babel } from '@rollup/plugin-babel'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    babel({
      presets: [
        [
          '@babel/preset-env',
          {
            targets: {
              chrome: '49',
              safari: '10',
              android: '4.4',
              ios: '9',
            },
            useBuiltIns: 'usage',
            corejs: 3,
          },
        ],
      ],
      exclude: 'node_modules/**',
    }),
  ],
  build: {
    emptyOutDir: true,
    lib: {
      entry: resolve(__dirname, 'packages/web-capacity-limit.js'),
      name: 'WebCapacityLimit',
      fileName: 'web-capacity-limit',
      formats: ['iife'],
    },
    outDir: 'dist',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
      },
    },
  },
})
