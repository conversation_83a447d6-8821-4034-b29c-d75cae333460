stages:
  - build
  - release

variables:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

.node_setup: &node_setup
  image: node:${NODE_VERSION}
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
      - node_modules/
    policy: pull-push
before_script:
  - npm install -g pnpm@${PNPM_VERSION}
  - pnpm config set store-dir .pnpm-store

build-job:
  <<: *node_setup
  stage: build
  script:
    - pnpm install
    - pnpm run build:jssdk
  artifacts:
    paths:
      - dist/

create-release:
  stage: release
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  script:
    - |
      if [[ $CI_COMMIT_TAG =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        VERSION=${CI_COMMIT_TAG#v}
        pnpm version $VERSION --no-git-tag-version
      fi
    - cd dist && tar -czf ../release.tar.gz ./*
  rules:
    - if: $CI_COMMIT_TAG
  release:
    name: 'Release $CI_COMMIT_TAG'
    tag_name: $CI_COMMIT_TAG
    description: 'Release created from tag $CI_COMMIT_TAG'
    assets:
      links:
        - name: 'release.tar.gz'
          url: '${CI_PROJECT_URL}/-/jobs/artifacts/${CI_COMMIT_REF_NAME}/raw/release.tar.gz?job=create-release'
          link_type: 'package'
  artifacts:
    paths:
      - release.tar.gz
