import { fileURLToPath, URL } from 'node:url'
import path from 'node:path'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    UnoCSS(),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ['vue', 'vue-router', '@vueuse/core'],
      // 自动导入的文件类型
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/, // .md
      ],
      // 生成相应的自动导入的 .d.ts 声明文件
      dts: 'src/types/auto-imports.d.ts',
      // 自动导入目录下的模块
      dirs: [
        'src/composables/**',
        'src/utils/**',
        'src/stores/**',
        'src/components/**', // 自动导入组件
      ],
      // eslint globals Docs - https://eslint.org/docs/user-guide/configuring/language-options#specifying-globals
      eslintrc: {
        enabled: true, // 生成 .eslintrc-auto-import.json 文件
        filepath: './.eslintrc-auto-import.json', // 文件路径
        globalsPropValue: true, // 设置为 true 表示这些全局变量为只读
      },
    }),
    // 组件自动导入插件
    Components({
      // 自动导入的组件目录
      dirs: ['src/components'],
      // 组件的有效文件扩展名
      extensions: ['vue'],
      // 生成组件类型声明文件
      dts: 'src/types/components.d.ts',
      // 解析器
      resolvers: [],
      // 包含/排除规则
      include: [/\.vue$/, /\.vue\?vue/],
      exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/, /[\\/]\.nuxt[\\/]/],
    }),
    // SVG 图标插件
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
      // 指定 symbolId 格式 (简化版，不需要 dir)
      symbolId: 'icon-[name]',
      // 插入位置
      inject: 'body-last',
      // 自定义 DOM ID
      customDomId: '__svg__icons__dom__',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use "@/styles/mixins.scss" as *;
        `,
        // 使用 sass-embedded 的现代 API，解决 legacy-js-api 警告
        api: 'modern-compiler',
      },
    },
  },
})
