import { defineConfig, presetAttributify, presetIcons, presetWind3 } from 'unocss'

export default defineConfig({
  presets: [
    presetWind3(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: 'inline-block',
        verticalAlign: 'middle',
      },
    }),
  ],
  theme: {
    colors: {
      // Apple 系统颜色 - 使用 CSS 变量
      primary: 'var(--color-primary)',
      'primary-50': 'var(--color-primary-50)',
      'primary-100': 'var(--color-primary-100)',
      'primary-200': 'var(--color-primary-200)',
      'primary-300': 'var(--color-primary-300)',
      'primary-400': 'var(--color-primary-400)',
      'primary-500': 'var(--color-primary-500)',
      'primary-600': 'var(--color-primary-600)',
      'primary-700': 'var(--color-primary-700)',
      'primary-800': 'var(--color-primary-800)',
      'primary-900': 'var(--color-primary-900)',

      success: 'var(--color-success)',
      'success-50': 'var(--color-success-50)',
      'success-100': 'var(--color-success-100)',
      'success-500': 'var(--color-success-500)',
      'success-600': 'var(--color-success-600)',
      'success-700': 'var(--color-success-700)',

      warning: 'var(--color-warning)',
      'warning-50': 'var(--color-warning-50)',
      'warning-100': 'var(--color-warning-100)',
      'warning-500': 'var(--color-warning-500)',
      'warning-600': 'var(--color-warning-600)',

      error: 'var(--color-error)',
      'error-50': 'var(--color-error-50)',
      'error-100': 'var(--color-error-100)',
      'error-500': 'var(--color-error-500)',
      'error-600': 'var(--color-error-600)',

      info: 'var(--color-info)',
      'info-50': 'var(--color-info-50)',
      'info-100': 'var(--color-info-100)',
      'info-500': 'var(--color-info-500)',
      'info-600': 'var(--color-info-600)',

      purple: 'var(--color-purple)',
      'purple-50': 'var(--color-purple-50)',
      'purple-100': 'var(--color-purple-100)',
      'purple-500': 'var(--color-purple-500)',
      'purple-600': 'var(--color-purple-600)',

      // Apple 中性色系
      'gray-50': 'var(--color-gray-50)',
      'gray-100': 'var(--color-gray-100)',
      'gray-200': 'var(--color-gray-200)',
      'gray-300': 'var(--color-gray-300)',
      'gray-400': 'var(--color-gray-400)',
      'gray-500': 'var(--color-gray-500)',
      'gray-600': 'var(--color-gray-600)',
      'gray-700': 'var(--color-gray-700)',
      'gray-800': 'var(--color-gray-800)',
      'gray-900': 'var(--color-gray-900)',

      // Apple 语义化颜色
      'text-primary': 'var(--color-text-primary)',
      'text-secondary': 'var(--color-text-secondary)',
      'text-tertiary': 'var(--color-text-tertiary)',
      'text-inverse': 'var(--color-text-inverse)',

      'bg-primary': 'var(--color-bg-primary)',
      'bg-secondary': 'var(--color-bg-secondary)',
      'bg-tertiary': 'var(--color-bg-tertiary)',

      'surface-primary': 'var(--color-surface-primary)',
      'surface-secondary': 'var(--color-surface-secondary)',

      'border-light': 'var(--color-border-light)',
      'border-normal': 'var(--color-border-normal)',
      'border-dark': 'var(--color-border-dark)',
    },
    spacing: {
      '0': 'var(--spacing-0)',
      '1': 'var(--spacing-1)',
      '2': 'var(--spacing-2)',
      '3': 'var(--spacing-3)',
      '4': 'var(--spacing-4)',
      '5': 'var(--spacing-5)',
      '6': 'var(--spacing-6)',
      '8': 'var(--spacing-8)',
      '10': 'var(--spacing-10)',
      '12': 'var(--spacing-12)',
      '16': 'var(--spacing-16)',
      '20': 'var(--spacing-20)',
      '24': 'var(--spacing-24)',
    },
    borderRadius: {
      xs: 'var(--radius-xs)',
      sm: 'var(--radius-sm)',
      md: 'var(--radius-md)',
      lg: 'var(--radius-lg)',
      xl: 'var(--radius-xl)',
      '2xl': 'var(--radius-2xl)',
      '3xl': 'var(--radius-3xl)',
      full: 'var(--radius-full)',
    },
    boxShadow: {
      xs: 'var(--shadow-xs)',
      sm: 'var(--shadow-sm)',
      md: 'var(--shadow-md)',
      lg: 'var(--shadow-lg)',
      xl: 'var(--shadow-xl)',
      card: 'var(--shadow-card)',
      modal: 'var(--shadow-modal)',
    },
    fontSize: {
      largeTitle: 'var(--font-size-largeTitle)',
      title1: 'var(--font-size-title1)',
      title2: 'var(--font-size-title2)',
      title3: 'var(--font-size-title3)',
      headline: 'var(--font-size-headline)',
      body: 'var(--font-size-body)',
      callout: 'var(--font-size-callout)',
      subheadline: 'var(--font-size-subheadline)',
      footnote: 'var(--font-size-footnote)',
      caption1: 'var(--font-size-caption1)',
      caption2: 'var(--font-size-caption2)',
    },
    fontWeight: {
      ultraLight: 'var(--font-weight-ultraLight)',
      thin: 'var(--font-weight-thin)',
      light: 'var(--font-weight-light)',
      regular: 'var(--font-weight-regular)',
      medium: 'var(--font-weight-medium)',
      semibold: 'var(--font-weight-semibold)',
      bold: 'var(--font-weight-bold)',
      heavy: 'var(--font-weight-heavy)',
      black: 'var(--font-weight-black)',
    },
    transitionTimingFunction: {
      spring: 'var(--ease-spring)',
      bounce: 'var(--ease-bounce)',
    },
    transitionDuration: {
      fast: 'var(--transition-fast)',
      normal: 'var(--transition-normal)',
      slow: 'var(--transition-slow)',
    },
  },
  shortcuts: {
    // Apple 风格按钮 - 替代 global.css 中的 .btn
    'btn-apple':
      'inline-flex items-center justify-center px-4 py-3 text-body font-medium rounded-lg transition-all duration-normal apple-button-effect min-h-11 cursor-pointer select-none',
    'btn-apple-primary':
      'btn-apple text-white bg-primary hover:bg-primary-600 active:bg-primary-700 apple-focus-ring',
    'btn-apple-secondary':
      'btn-apple text-primary bg-surface-primary border border-border-normal hover:bg-gray-50 apple-focus-ring',
    'btn-apple-danger':
      'btn-apple text-white bg-error hover:bg-error-600 active:bg-error-600 apple-focus-ring',

    // Apple 风格卡片 - 替代 global.css 中的 .card
    'card-apple':
      'bg-surface-primary rounded-lg shadow-card border border-border-normal overflow-hidden',
    'card-apple-header': 'p-4 border-b border-border-light bg-gray-50',
    'card-apple-body': 'p-4',
    'card-apple-footer': 'p-4 border-t border-border-light bg-gray-50',

    // Apple 风格输入框 - 替代 global.css 中的 .form-input
    'input-apple':
      'w-full px-4 py-3 text-body bg-surface-primary border border-border-normal rounded-lg apple-focus-ring min-h-11 appearance-none',

    // Apple 风格列表 - 替代 global.css 中的 .list
    'list-apple': 'bg-surface-primary rounded-lg overflow-hidden',
    'list-item-apple':
      'flex items-center px-4 py-3 border-b border-border-light last:border-b-0 active:bg-gray-50 transition-colors duration-fast min-h-11 cursor-pointer',

    // 毛玻璃效果
    'glass-apple': 'glass-effect',

    // 安全区域
    'safe-top': 'pt-[env(safe-area-inset-top)]',
    'safe-bottom': 'pb-[env(safe-area-inset-bottom)]',
    'safe-left': 'pl-[env(safe-area-inset-left)]',
    'safe-right': 'pr-[env(safe-area-inset-right)]',

    // 滚动条隐藏
    'scrollbar-hide': 'scrollbar-hide',
  },
})
