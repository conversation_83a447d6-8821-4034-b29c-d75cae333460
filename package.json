{"name": "life-jssdk-debugger", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "format": "prettier --write src/", "es-check": "es-check es5 './packages/life-jssdk.js'", "lint:jssdk": "eslint --fix './packages/life-jssdk.js'", "build:capacity": "vite build --config vite.capacity.config.js", "build:web-capacity": "vite build --config vite.web-capacity.config.js", "build:module-builder": "vite build --config vite.module-builder.config.js", "build:web-capacity-all": "npm run build:web-capacity && npm run build:module-builder", "generate:templates": "node packages/web-capacity/scripts/generate-templates.js", "build:templates-json": "node packages/web-capacity/scripts/build-templates-json.js", "build:web-capacity-full": "npm run generate:templates && npm run build:web-capacity-all", "build:jssdk": "npm run lint:jssdk && npm run es-check && vite build --config vite.jssdk.config.js", "prepare": "husky", "commit": "czg", "lint-staged": "lint-staged", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"@vueuse/core": "^13.6.0", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@iconify-json/carbon": "^1.2.8", "@rollup/plugin-babel": "^6.0.4", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.9.0", "@unocss/preset-attributify": "66.1.0-beta.3", "@unocss/preset-icons": "66.1.0-beta.3", "@unocss/preset-uno": "66.1.0-beta.3", "@vitejs/plugin-legacy": "^5.2.0", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.5.1", "core-js": "^3.39.0", "cz-git": "^1.11.0", "czg": "^1.11.0", "es-check": "^7.2.1", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "npm-run-all2": "^7.0.1", "prettier": "^3.3.3", "sass-embedded": "^1.89.2", "terser": "^5.27.0", "typescript": "~5.6.3", "unocss": "66.1.0-beta.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vconsole": "^3.15.1", "vite": "^5.4.10", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.5.4", "vitepress": "^1.6.3", "vue-tsc": "^2.1.10"}, "config": {"commitizen": {"path": "node_modules/cz-git", "config": "./cz.config.js"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,less,scss,sass}": ["prettier --write"], "*.{json,md}": ["prettier --write"]}}