# fx-js-sdk

纷享生活的 js-sdk，用于纷享生活app与h5页面之间交互通信。

## 目录结构

> jssdk，使用es5语法，禁止使用es6+语法，开发阶段会有eslint检查，打包时会使用es-check检查

```
packages/
├── life-jssdk.js // jssdk
├── web-capacity-limit.js // 页面资源收集+js能力限制
...
src/ 代码调试
...
vite.jssdk.config.js // jssdk打包配置文件
vite.capacity-limit.config.js // 页面资源收集+js能力限制打包配置文件
```

## node版本

> 18.19.0

## 下载依赖

```sh
pnpm install
```

## 调试

```sh
pnpm dev
```

## 打包

> 打包时会检查语法，如果语法错误，会报错，需要手动修改

```sh
pnpm build:jssdk # 打包jssdk
pnpm build:capacity-limit # 打包页面资源收集+js能力限制
```
