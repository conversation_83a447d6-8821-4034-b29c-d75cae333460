import pluginVue from 'eslint-plugin-vue'
import vueTsEslintConfig from '@vue/eslint-config-typescript'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
// 导入自动生成的全局变量配置
import autoImportGlobals from './.eslintrc-auto-import.json' assert { type: 'json' }

export default [
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
    languageOptions: {
      globals: {
        ...autoImportGlobals.globals,
      },
    },
  },

  {
    name: 'app/files-to-ignore',
    ignores: [
      '**/dist/**',
      '**/dist-ssr/**',
      '**/coverage/**',
      '**/.vitepress/**',
      '**/packages/web-capacity/templates/polyfills.js',
    ],
  },

  ...pluginVue.configs['flat/essential'],
  ...vueTsEslintConfig(),
  skipFormatting,
  {
    name: 'app/life-jssdk',
    files: ['packages/life-jssdk.js', 'packages/launch-wechat-mini-program.js'],
    languageOptions: {
      ecmaVersion: 5,
    },
    rules: {
      // 禁止 ES6+ 的语法
      'no-restricted-syntax': [
        'error',
        {
          selector: 'ArrowFunctionExpression',
          message: '箭头函数是 ES6+ 的特性，请使用普通函数。',
        },
        {
          selector: 'ClassDeclaration, ClassExpression',
          message: '类声明是 ES6+ 的特性，请使用函数构造器。',
        },
        {
          selector: 'TemplateLiteral, TemplateElement',
          message: '模板字符串是 ES6+ 的特性，请使用字符串拼接。',
        },
        {
          selector: 'ForOfStatement',
          message: 'for...of 是 ES6+ 的特性，请使用普通 for 循环。',
        },
        {
          selector: 'ImportDeclaration',
          message: 'import 是 ES6+ 的特性，请使用 require()。',
        },
        {
          selector: 'ExportNamedDeclaration',
          message: 'export 是 ES6+ 的特性，请使用 module.exports。',
        },
        {
          selector: 'ExportDefaultDeclaration',
          message: 'export default 是 ES6+ 的特性，请使用 module.exports。',
        },
        {
          selector: 'ExportAllDeclaration',
          message: 'export * 是 ES6+ 的特性，请使用 module.exports。',
        },
        {
          selector: 'LetDeclaration',
          message: 'let 是 ES6+ 的特性，请使用 var。',
        },
        {
          selector: 'VariableDeclaration[kind="const"]',
          message: 'const 是 ES6+ 的特性，请使用 var。',
        },
        {
          selector: 'SpreadElement',
          message: '扩展运算符 (...) 是 ES6+ 的特性，请使用其他方式替代。',
        },
        {
          selector: 'RestElement',
          message: '剩余参数 (...) 是 ES6+ 的特性，请使用 arguments 对象。',
        },
        {
          selector: 'ObjectPattern',
          message: '解构赋值是 ES6+ 的特性，请使用普通属性访问。',
        },
        {
          selector: 'ArrayPattern',
          message: '解构赋值是 ES6+ 的特性，请使用普通索引访问。',
        },
        {
          selector: 'NewExpression[callee.name="Set"]',
          message: 'Set 是 ES6+ 的特性，请使用对象或数组。',
        },
        {
          selector: 'NewExpression[callee.name="Map"]',
          message: 'Map 是 ES6+ 的特性，请使用对象。',
        },
        {
          selector: 'NewExpression[callee.name="WeakSet"]',
          message: 'WeakSet 是 ES6+ 的特性，请使用对象或数组。',
        },
        {
          selector: 'NewExpression[callee.name="WeakMap"]',
          message: 'WeakMap 是 ES6+ 的特性，请使用对象。',
        },
        {
          selector: 'NewExpression[callee.name="Promise"]',
          message: 'Promise 是 ES6+ 的特性，请使用回调函数。',
        },
        {
          selector: 'FunctionDeclaration[generator=true]',
          message: '生成器 (function*) 是 ES6+ 的特性，请避免使用。',
        },
        {
          selector: 'FunctionExpression[generator=true]',
          message: '生成器 (function*) 是 ES6+ 的特性，请避免使用。',
        },
        {
          selector: 'AwaitExpression',
          message: 'async/await 是 ES8 的特性，请使用回调函数。',
        },
        // 模板字符串嵌套
        { selector: 'TemplateElement', message: '模板字符串是 ES6+ 的特性，请使用字符串拼接。' },
        // 默认参数
        { selector: 'AssignmentPattern', message: '函数参数的默认值是 ES6+ 的特性，请避免使用。' },
        // 剩余参数
        {
          selector: 'FunctionDeclaration[params.0.type="RestElement"]',
          message: '函数剩余参数是 ES6+ 的特性，请使用 arguments 替代。',
        },
        // 高级解构
        {
          selector: 'ObjectPattern > Property[value.type="AssignmentPattern"]',
          message: '解构赋值中的默认值是 ES6+ 的特性，请避免使用。',
        },
        // 对象简写属性
        {
          selector: 'Property[shorthand=true]',
          message: '对象属性简写是 ES6+ 的特性，请使用完整的属性定义。',
        },
        // 标签模板
        {
          selector: 'TaggedTemplateExpression',
          message: '标签模板字符串是 ES6+ 的特性，请避免使用。',
        },
        // 可计算属性
        {
          selector: 'Property[computed=true]',
          message: '计算属性名是 ES6+ 的特性，请使用静态属性名。',
        },
        // Symbol 内置方法
        {
          selector: 'MemberExpression[object.name="Symbol"]',
          message: 'Symbol 的内置方法是 ES6+ 的特性，请避免使用。',
        },
        // 二进制/八进制字面量
        {
          selector: 'Literal[value=/^0[bBoO]/]',
          message: '二进制和八进制字面量是 ES6+ 的特性，请使用十进制。',
        },
        // yield 关键字
        { selector: 'YieldExpression', message: 'yield 是生成器的特性，请避免使用。' },

        {
          selector: 'OptionalMemberExpression',
          message: '可选链操作符 (?.) 是 ES2020 的特性，请避免使用。',
        },
        {
          selector: 'LogicalExpression[operator="??"]',
          message: '空值合并运算符 (??) 是 ES2020 的特性，请避免使用。',
        },
        {
          selector: 'Literal[regex.flags=/u|y/]',
          message: '正则表达式中的 u 或 y 修饰符是 ES6+ 的特性，请避免使用。',
        },
        {
          selector: 'FunctionDeclaration[async=true]',
          message: 'async 是 ES8 的特性，请使用回调函数替代。',
        },
        { selector: 'AwaitExpression', message: 'await 是 ES8 的特性，请使用回调函数替代。' },
        {
          selector: 'AssignmentPattern',
          message: '解构赋值默认值是 ES6+ 的特性',
        },
      ],

      // 禁止 ES6+ 全局对象
      'no-restricted-globals': [
        'error',
        {
          name: 'Set',
          message: 'Set 是 ES6+ 的特性，请使用对象或数组。',
        },
        {
          name: 'Map',
          message: 'Map 是 ES6+ 的特性，请使用对象。',
        },
        {
          name: 'WeakSet',
          message: 'WeakSet 是 ES6+ 的特性，请使用对象或数组。',
        },
        {
          name: 'WeakMap',
          message: 'WeakMap 是 ES6+ 的特性，请使用对象。',
        },
        {
          name: 'Promise',
          message: 'Promise 是 ES6+ 的特性，请使用回调函数。',
        },
        {
          name: 'Symbol',
          message: 'Symbol 是 ES6+ 的特性，请避免使用。',
        },
        { name: 'Proxy', message: 'Proxy 是 ES6+ 的特性，请避免使用。' },
        { name: 'Reflect', message: 'Reflect 是 ES6+ 的特性，请避免使用。' },
        { name: 'globalThis', message: 'globalThis 是 ES2020 的特性，请避免使用。' },
      ],

      // 禁止使用 ES6+ 的对象方法
      'no-restricted-properties': [
        'error',
        {
          object: 'Object',
          property: 'assign',
          message: 'Object.assign 是 ES6+ 的特性，请使用手动拷贝实现。',
        },
        {
          object: 'Object',
          property: 'values',
          message: 'Object.values 是 ES8 的特性，请使用手动遍历替代。',
        },
        {
          object: 'Object',
          property: 'entries',
          message: 'Object.entries 是 ES8 的特性，请使用手动遍历替代。',
        },
        {
          object: 'Array',
          property: 'from',
          message: 'Array.from 是 ES6+ 的特性，请使用手动遍历替代。',
        },
        {
          object: 'Array',
          property: 'find',
          message: 'Array.find 是 ES6+ 的特性，请使用手动循环实现。',
        },
        {
          object: 'Array',
          property: 'includes',
          message: 'Array.includes 是 ES7 的特性，请使用 indexOf 替代。',
        },
        {
          object: 'String',
          property: 'includes',
          message: 'String.includes 是 ES6+ 的特性，请使用 indexOf 替代。',
        },
        { object: 'Array', property: 'flat', message: 'Array.flat 是 ES2019 的特性，请避免使用。' },
        {
          object: 'Promise',
          property: 'allSettled',
          message: 'Promise.allSettled 是 ES2020 的特性，请避免使用。',
        },
        {
          object: 'String',
          property: 'startsWith',
          message: 'String.startsWith 是 ES6+ 的特性',
        },
        {
          object: 'Array',
          property: 'findIndex',
          message: 'Array.findIndex 是 ES6+ 的特性',
        },
      ],
    },
  },
]
