import { defineConfig } from 'vite'
import { babel } from '@rollup/plugin-babel'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    babel({
      presets: [
        [
          '@babel/preset-env',
          {
            targets: {
              chrome: '49',
              safari: '10',
              android: '4.4',
              ios: '9',
            },
            useBuiltIns: 'usage',
            corejs: 3,
            modules: false, // 保持ES模块用于tree-shaking
          },
        ],
      ],
      exclude: 'node_modules/**',
    }),
  ],
  build: {
    emptyOutDir: false, // 不清空输出目录，避免删除其他构建产物
    lib: {
      entry: resolve(__dirname, 'packages/web-capacity/index.js'),
      name: 'WebCapacityLimit',
      fileName: 'web-capacity-limit',
      formats: ['iife', 'es'],
    },
    rollupOptions: {
      output: {
        inlineDynamicImports: true, // 启用内联动态导入以避免代码分割
      },
    },
    outDir: 'dist',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false, // 保留console.log用于调试
        drop_debugger: true,
      },
      mangle: {
        reserved: ['WebCapacityLimit'], // 保留全局变量名
      },
    },
  },
})
