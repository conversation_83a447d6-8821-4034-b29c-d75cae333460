/**
 * Web Capacity Limit - 主入口文件
 * 提供完整的功能限制和资源收集能力
 */

// 导入核心模块
import { Utils } from './core/utils.js'
import { ResourceCollector } from './core/resource-collector.js'
import { FeatureRestrictor } from './restrictions/index.js'

// 默认配置 - 启用所有功能
const DEFAULT_CONFIG = {
  resourceCollection: true,
  enabledModules: [
    'geolocation',
    'clipboard',
    'mediaDevices',
    'permissions',
    'credentials',
    'bluetooth',
    'share',
    'network',
    'storage',
    'mediaElement',
    'notification',
    'deviceOrientation',
    'deviceMotion',
    'print',
  ],
}

/**
 * 初始化Web Capacity Limit
 * @param {Object} config - 配置对象
 * @param {boolean} config.resourceCollection - 是否启用资源收集
 * @param {string[]} config.enabledModules - 启用的限制模块列表
 */
function initialize(config = DEFAULT_CONFIG) {
  try {
    // 合并配置
    const finalConfig = { ...DEFAULT_CONFIG, ...config }

    // 初始化功能限制器
    if (finalConfig.enabledModules.length > 0) {
      const featureRestrictor = FeatureRestrictor.getInstance(finalConfig.enabledModules)
      featureRestrictor.init()
      console.log('[WebCapacity] 功能限制已应用，限制模块:', finalConfig.enabledModules)
    }

    // 初始化资源收集器
    if (finalConfig.resourceCollection) {
      const resourceCollector = ResourceCollector.getInstance()

      // 暴露全局方法
      Object.defineProperties(window, {
        _getAllResourceUrls: {
          value: () => resourceCollector.init(),
          writable: false,
          configurable: false,
        },
        _cleanupWatch: {
          value: () => resourceCollector.cleanup(),
          writable: false,
          configurable: false,
        },
      })

      console.log('[WebCapacity] 资源收集器已初始化')
    }

    console.log('[WebCapacity] 初始化完成')
    return true
  } catch (error) {
    console.error('[WebCapacity] 初始化失败:', error)
    return false
  }
}

// 自动初始化（使用默认配置）
initialize()

// 导出模块供外部使用
export { Utils, ResourceCollector, FeatureRestrictor, initialize, DEFAULT_CONFIG }
