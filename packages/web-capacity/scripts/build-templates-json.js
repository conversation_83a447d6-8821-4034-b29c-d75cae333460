#!/usr/bin/env node

/**
 * 模板代码打包脚本
 * 将所有模块模板代码打包成一个 JSON 对象
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { transformSync } from '@babel/core'

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 路径配置
const RESTRICTIONS_DIR = path.join(__dirname, '../restrictions')
const TEMPLATES_DIR = path.join(__dirname, '../templates')
const OUTPUT_FILE = path.join(__dirname, '../../../dist/templates.json')
const SELECT_OPTIONS_FILE = path.join(__dirname, '../../../dist/select-options.json')

/**
 * Babel 配置 - 转换为 ES5 兼容代码
 */
const BABEL_CONFIG = {
  presets: [
    [
      '@babel/preset-env',
      {
        targets: {
          chrome: '49',
          safari: '10',
          android: '4.1',
          ios: '8',
        },
        modules: false,
        useBuiltIns: false,
        loose: true,
        spec: false,
        debug: false,
      },
    ],
  ],
}

/**
 * 使用 Babel 转换代码为 ES5
 */
function transformToES5(code, filename = 'template') {
  try {
    if (!code || code.trim().length === 0) {
      console.warn(`⚠️  代码为空 (${filename})，跳过转换`)
      return code
    }

    const result = transformSync(code, {
      ...BABEL_CONFIG,
      filename,
      sourceMaps: false,
      compact: false,
      comments: true,
      minified: false,
    })

    if (!result || !result.code) {
      console.warn(`⚠️  Babel 转换失败 (${filename})，使用原始代码`)
      return code
    }

    // 清理转换后的代码
    let cleanCode = result.code
      .replace(/\/\*#__PURE__\*\//g, '')
      .replace(/export\s+const\s+(\w+)\s*=/g, 'var $1 =')
      .replace(/export\s+class\s+/g, '')
      .replace(/export\s+/g, '')
      .replace(/exports\.\w+\s*=\s*[^;]+;?\s*/g, '')
      .replace(/module\.exports\s*=\s*[^;]+;?\s*/g, '')
      .replace(/Object\.defineProperty\(exports,[\s\S]*?\}\);?\s*/g, '')
      .replace(/\n\s*\n\s*\n/g, '\n\n')

    return cleanCode
  } catch (error) {
    console.warn(`⚠️  Babel 转换出错 (${filename}):`, error.message)
    return code
  }
}

/**
 * 读取并解析模块文件
 */
function parseModuleFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8')

  // 找到 apply() 方法的开始位置
  const applyStart = content.indexOf('apply() {')
  if (applyStart === -1) {
    console.warn(`警告: ${filePath} 中未找到 apply 方法`)
    return null
  }

  // 找到方法体的开始位置（第一个 { 之后）
  const methodBodyStart = content.indexOf('{', applyStart) + 1

  // 使用括号计数来找到匹配的结束括号
  let braceCount = 1
  let methodBodyEnd = methodBodyStart

  while (braceCount > 0 && methodBodyEnd < content.length) {
    const char = content[methodBodyEnd]
    if (char === '{') {
      braceCount++
    } else if (char === '}') {
      braceCount--
    }
    methodBodyEnd++
  }

  if (braceCount !== 0) {
    console.warn(`警告: ${filePath} 中 apply 方法的括号不匹配`)
    return null
  }

  // 提取方法体内容（不包括最后的 }）
  let applyContent = content.substring(methodBodyStart, methodBodyEnd - 1).trim()

  // 移除 this. 引用，因为在模板中不需要
  applyContent = applyContent.replace(/this\./g, '')

  // 替换 restrictions 引用为具体的错误消息
  applyContent = applyContent.replace(/restrictions\.(\w+)/g, () => {
    return `function() { throw new Error('请使用app的相关功能') }`
  })

  // 使用 Babel 转换为 ES5 兼容代码
  applyContent = transformToES5(applyContent, path.basename(filePath))

  // 移除多余的缩进
  const lines = applyContent.split('\n')
  const minIndent = Math.min(
    ...lines
      .filter((line) => line.trim())
      .map((line) => {
        const match = line.match(/^(\s*)/)
        return match ? match[1].length : 0
      }),
  )

  const cleanedLines = lines.map((line) => {
    if (line.trim()) {
      return line.substring(minIndent)
    }
    return line
  })

  return cleanedLines.join('\n').trim()
}

/**
 * 获取模块信息
 */
function getModuleInfo(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8')

  const idMatch = content.match(/id:\s*['"`]([^'"`]+)['"`]/)
  const nameMatch = content.match(/name:\s*['"`]([^'"`]+)['"`]/)
  const descMatch = content.match(/description:\s*['"`]([^'"`]+)['"`]/)

  if (idMatch && nameMatch && descMatch) {
    return {
      id: idMatch[1],
      name: nameMatch[1],
      description: descMatch[1],
    }
  }

  return null
}

/**
 * 生成限制模块模板
 */
function generateRestrictionTemplates() {
  const templates = {}

  // 读取所有限制模块文件
  const files = fs
    .readdirSync(RESTRICTIONS_DIR)
    .filter((file) => file.endsWith('.js') && file !== 'index.js')

  console.log(`🔍 发现 ${files.length} 个模块文件`)

  for (const file of files) {
    const filePath = path.join(RESTRICTIONS_DIR, file)
    const moduleInfo = getModuleInfo(filePath)

    if (!moduleInfo) {
      console.warn(`⚠️  无法解析模块信息: ${file}`)
      continue
    }

    const applyContent = parseModuleFile(filePath)
    if (applyContent) {
      templates[moduleInfo.id] = `\n// ${moduleInfo.name}限制\n${applyContent}\n`
      console.log(`✅ 已处理模块: ${moduleInfo.id} (${moduleInfo.name})`)
    } else {
      console.warn(`⚠️  无法解析模块内容: ${file}`)
    }
  }

  return templates
}

/**
 * 生成 polyfills 模板
 */
function generatePolyfillsTemplate() {
  const polyfillsPath = path.join(TEMPLATES_DIR, 'polyfills.js')

  if (!fs.existsSync(polyfillsPath)) {
    console.warn('⚠️  polyfills.js 文件不存在，跳过生成')
    return '// polyfills.js 未找到'
  }

  try {
    const polyfillsContent = fs.readFileSync(polyfillsPath, 'utf-8')

    // 移除文件开头的注释块（保留代码中的注释）
    const cleanContent = polyfillsContent
      .replace(/^\/\*\*[\s\S]*?\*\/\s*/, '') // 移除开头的文档注释
      .trim()

    console.log('✅ polyfills 模板生成成功')
    return cleanContent
  } catch (error) {
    console.warn('⚠️  生成 polyfills 模板失败:', error.message)
    return '// polyfills 生成失败: ' + error.message
  }
}

/**
 * 生成资源收集器模板
 */
function generateResourceCollectorTemplate() {
  const resourceCollectorPath = path.join(__dirname, '../core/resource-collector.js')
  const utilsPath = path.join(__dirname, '../core/utils.js')

  if (!fs.existsSync(resourceCollectorPath) || !fs.existsSync(utilsPath)) {
    console.warn('⚠️  资源收集器或工具文件不存在，跳过生成')
    return '// 资源收集器未找到'
  }

  try {
    const resourceCollectorContent = fs.readFileSync(resourceCollectorPath, 'utf-8')
    const utilsContent = fs.readFileSync(utilsPath, 'utf-8')

    // 移除 import 语句
    const cleanUtilsContent = utilsContent.replace(/import.*?from.*?['"].*?['"];?\s*/g, '')
    const cleanResourceCollectorContent = resourceCollectorContent.replace(
      /import.*?from.*?['"].*?['"];?\s*/g,
      '',
    )

    // 创建完整的资源收集器模板（不再包含 polyfills，因为已经集成到 start 中）
    const resourceCollectorTemplate = `
${cleanUtilsContent}

${cleanResourceCollectorContent}

// 初始化资源收集器
var resourceCollector = ResourceCollector.getInstance();

// 暴露全局方法
if (typeof window !== 'undefined') {
  Object.defineProperties(window, {
    _getAllResourceUrls: {
      value: function() { return resourceCollector.init(); },
      writable: false,
      configurable: false,
    },
    _cleanupWatch: {
      value: function() { return resourceCollector.cleanup(); },
      writable: false,
      configurable: false,
    },
  });
}

console.log('[WebCapacity] 资源收集器已初始化');`

    // 使用 Babel 转换为 ES5 兼容代码
    console.log('🔄 正在转换资源收集器源码为 ES5...')
    return transformToES5(resourceCollectorTemplate, 'resource-collector-template')
  } catch (error) {
    console.warn('⚠️  生成资源收集器模板失败:', error.message)
    return '// 资源收集器生成失败: ' + error.message
  }
}

/**
 * 生成选择器选项数据
 */
function generateSelectOptions() {
  console.log('🔄 正在生成选择器选项数据...')

  // 读取所有限制模块文件
  const files = fs
    .readdirSync(RESTRICTIONS_DIR)
    .filter((file) => file.endsWith('.js') && file !== 'index.js')

  const selectOptions = []

  for (const file of files) {
    const filePath = path.join(RESTRICTIONS_DIR, file)
    const moduleInfo = getModuleInfo(filePath)

    if (moduleInfo) {
      selectOptions.push({
        label: moduleInfo.name,
        value: moduleInfo.id,
      })
      console.log(`✅ 已添加选项: ${moduleInfo.name} (${moduleInfo.id})`)
    } else {
      console.warn(`⚠️  无法解析模块信息: ${file}`)
    }
  }

  // 添加资源收集器选项
  selectOptions.push({
    label: '资源收集器',
    value: 'resourceCollector',
  })
  console.log('✅ 已添加选项: 资源收集器 (resourceCollector)')

  // 按 label 排序
  selectOptions.sort((a, b) => a.label.localeCompare(b.label))

  return selectOptions
}

/**
 * 构建模板 JSON 对象
 */
function buildTemplatesJson() {
  console.log('🚀 开始构建模板 JSON 对象...')

  // 生成限制模块模板
  const restrictionTemplates = generateRestrictionTemplates()

  // 生成 polyfills 模板
  console.log('🔄 正在生成 polyfills 模板...')
  const polyfillsTemplate = generatePolyfillsTemplate()

  // 生成资源收集器模板
  console.log('🔄 正在生成资源收集器模板...')
  const resourceCollectorTemplate = generateResourceCollectorTemplate()

  // 构建包含 polyfills 的 start 代码
  const startWithPolyfills =
    '(function() {\n  "use strict";\n\n' +
    '  // ES5 兼容性 polyfills\n' +
    polyfillsTemplate
      .split('\n')
      .map((line) => '  ' + line)
      .join('\n') +
    '\n'

  // 构建最终的 JSON 对象
  const templatesJson = {
    // 固定的包装代码（包含 polyfills）
    start: startWithPolyfills,
    end: '\n})();',

    // 资源收集器模板
    resourceCollector: `\n${resourceCollectorTemplate}\n`,

    // 所有限制模块模板
    ...restrictionTemplates,
  }

  return templatesJson
}

/**
 * 确保输出目录存在
 */
function ensureOutputDir() {
  const outputDir = path.dirname(OUTPUT_FILE)
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
    console.log(`📁 创建输出目录: ${outputDir}`)
  }
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🧪 模板代码打包脚本启动')
    console.log(`📂 输入目录: ${RESTRICTIONS_DIR}`)
    console.log(`📄 输出文件: ${OUTPUT_FILE}`)

    // 检查必要的目录和文件
    if (!fs.existsSync(RESTRICTIONS_DIR)) {
      throw new Error(`restrictions 目录不存在: ${RESTRICTIONS_DIR}`)
    }

    // 确保输出目录存在
    ensureOutputDir()

    // 构建模板 JSON 对象
    const templatesJson = buildTemplatesJson()

    // 生成选择器选项数据
    const selectOptions = generateSelectOptions()

    // 写入模板 JSON 文件
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(templatesJson, null, 2), 'utf-8')

    // 写入选择器选项 JSON 文件
    fs.writeFileSync(SELECT_OPTIONS_FILE, JSON.stringify(selectOptions, null, 2), 'utf-8')

    console.log('✅ 文件生成成功!')
    console.log('')
    console.log('📄 生成的文件:')
    console.log(`  1. 模板文件: ${OUTPUT_FILE}`)
    console.log(`     大小: ${(fs.statSync(OUTPUT_FILE).size / 1024).toFixed(2)} KB`)
    console.log(`  2. 选择器选项: ${SELECT_OPTIONS_FILE}`)
    console.log(`     大小: ${(fs.statSync(SELECT_OPTIONS_FILE).size / 1024).toFixed(2)} KB`)
    console.log('')
    console.log(`📊 统计信息:`)
    console.log(
      `  - 限制模块: ${Object.keys(templatesJson).filter((key) => !['start', 'end', 'resourceCollector'].includes(key)).length} 个`,
    )
    console.log(`  - Polyfills: 已集成到 start 中`)
    console.log(`  - 资源收集器: ${templatesJson.resourceCollector ? '已包含' : '未包含'}`)
    console.log(`  - 选择器选项: ${selectOptions.length} 个`)
    console.log('')
    console.log('📋 包含的模块:')

    Object.keys(templatesJson).forEach((key) => {
      if (!['start', 'end', 'resourceCollector'].includes(key)) {
        const option = selectOptions.find((opt) => opt.value === key)
        const label = option ? option.label : key
        console.log(`  - ${key} (${label})`)
      }
    })

    console.log('')
    console.log('💡 使用方法:')
    console.log('  // 模板文件（换行符已内置，无需手动添加）')
    console.log('  const templates = require("./dist/templates.json");')
    console.log('  const code = templates.start +')
    console.log('               templates.geolocation +')
    console.log('               templates.end;')
    console.log('  eval(code);')
    console.log('')
    console.log('  // 多模块拼接示例')
    console.log('  const code2 = templates.start +')
    console.log('                templates.geolocation +')
    console.log('                templates.clipboard +')
    console.log('                templates.resourceCollector +')
    console.log('                templates.end;')
    console.log('')
    console.log('  // 选择器选项')
    console.log('  const options = require("./dist/select-options.json");')
    console.log('  // [{ label: "蓝牙", value: "bluetooth" }, ...]')
  } catch (error) {
    console.error('❌ 构建失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { buildTemplatesJson, generateRestrictionTemplates, generateResourceCollectorTemplate }
