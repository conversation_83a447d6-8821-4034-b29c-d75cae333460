#!/usr/bin/env node

/**
 * 自动生成 ModuleBuilder 模板脚本
 * 从 restrictions 目录读取模块，生成对应的模板字符串
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { transformSync } from '@babel/core'

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 路径配置
const RESTRICTIONS_DIR = path.join(__dirname, '../restrictions')
const BUILDER_FILE = path.join(__dirname, '../builder/module-builder.js')

/**
 * Babel 配置 - 转换为 ES5 兼容代码
 */
const BABEL_CONFIG = {
  presets: [
    [
      '@babel/preset-env',
      {
        targets: {
          chrome: '49',
          safari: '10',
          android: '4.1',
          ios: '8',
        },
        modules: false, // 不转换模块语法，我们手动处理
        useBuiltIns: false, // 不自动引入 polyfill，手动处理
        loose: true, // 使用宽松模式，生成更简洁的代码
        spec: false, // 不严格遵循规范，优先考虑性能
        debug: false, // 不输出调试信息
      },
    ],
  ],
  // 移除单独的插件，让 @babel/preset-env 根据 targets 自动处理所有需要的转换
}

/**
 * 使用 Babel 转换代码为 ES5
 */
function transformToES5(code, filename = 'template') {
  try {
    // 先验证代码是否是有效的 JavaScript
    if (!code || code.trim().length === 0) {
      console.warn(`⚠️  代码为空 (${filename})，跳过转换`)
      return code
    }

    // 对于资源收集器模板，不使用包装函数，直接转换
    const result = transformSync(code, {
      ...BABEL_CONFIG,
      filename,
      sourceMaps: false,
      compact: false,
      comments: true,
      minified: false,
    })

    if (!result || !result.code) {
      console.warn(`⚠️  Babel 转换失败 (${filename})，使用原始代码`)
      return code
    }

    // 清理转换后的代码
    let cleanCode = result.code
      // 移除 Babel 添加的注释
      .replace(/\/\*#__PURE__\*\//g, '')
      // 移除 export 语句
      .replace(/export\s+const\s+(\w+)\s*=/g, 'var $1 =')
      .replace(/export\s+class\s+/g, '')
      .replace(/export\s+/g, '')
      // 移除 exports 语句
      .replace(/exports\.\w+\s*=\s*[^;]+;?\s*/g, '')
      .replace(/module\.exports\s*=\s*[^;]+;?\s*/g, '')
      // 移除 Object.defineProperty(exports, ...)
      .replace(/Object\.defineProperty\(exports,[\s\S]*?\}\);?\s*/g, '')
      // 移除多余的空行
      .replace(/\n\s*\n\s*\n/g, '\n\n')

    return cleanCode
  } catch (error) {
    console.warn(`⚠️  Babel 转换出错 (${filename}):`, error.message)
    console.warn(`   原始代码预览: ${code.substring(0, 200)}...`)
    return code
  }
}

/**
 * 读取并解析模块文件
 */
function parseModuleFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8')

  // 找到 apply() 方法的开始位置
  const applyStart = content.indexOf('apply() {')
  if (applyStart === -1) {
    console.warn(`警告: ${filePath} 中未找到 apply 方法`)
    return null
  }

  // 找到方法体的开始位置（第一个 { 之后）
  const methodBodyStart = content.indexOf('{', applyStart) + 1

  // 使用括号计数来找到匹配的结束括号
  let braceCount = 1
  let methodBodyEnd = methodBodyStart

  while (braceCount > 0 && methodBodyEnd < content.length) {
    const char = content[methodBodyEnd]
    if (char === '{') {
      braceCount++
    } else if (char === '}') {
      braceCount--
    }
    methodBodyEnd++
  }

  if (braceCount !== 0) {
    console.warn(`警告: ${filePath} 中 apply 方法的括号不匹配`)
    return null
  }

  // 提取方法体内容（不包括最后的 }）
  let applyContent = content.substring(methodBodyStart, methodBodyEnd - 1).trim()

  // 移除 this. 引用，因为在模板中不需要
  applyContent = applyContent.replace(/this\./g, '')

  // 替换 restrictions 引用为具体的错误消息
  applyContent = applyContent.replace(/restrictions\.(\w+)/g, () => {
    return `function() { throw new Error('请使用app的相关功能') }`
  })

  // 使用 Babel 转换为 ES5 兼容代码
  applyContent = transformToES5(applyContent, path.basename(filePath))

  // 移除多余的缩进
  const lines = applyContent.split('\n')
  const minIndent = Math.min(
    ...lines
      .filter((line) => line.trim())
      .map((line) => {
        const match = line.match(/^(\s*)/)
        return match ? match[1].length : 0
      }),
  )

  const cleanedLines = lines.map((line) => {
    if (line.trim()) {
      return line.substring(minIndent)
    }
    return line
  })

  return cleanedLines.join('\n').trim()
}

/**
 * 读取模块列表信息
 */
function getModuleListFromIndex() {
  const modules = []
  const files = fs
    .readdirSync(RESTRICTIONS_DIR)
    .filter((file) => file.endsWith('.js') && file !== 'index.js')

  for (const file of files) {
    const filePath = path.join(RESTRICTIONS_DIR, file)
    const moduleContent = fs.readFileSync(filePath, 'utf-8')

    // 提取模块信息
    const idMatch = moduleContent.match(/id:\s*['"`]([^'"`]+)['"`]/)
    const nameMatch = moduleContent.match(/name:\s*['"`]([^'"`]+)['"`]/)
    const descMatch = moduleContent.match(/description:\s*['"`]([^'"`]+)['"`]/)

    if (idMatch && nameMatch && descMatch) {
      modules.push({
        id: idMatch[1],
        name: nameMatch[1],
        description: descMatch[1],
      })
    }
  }

  return modules
}

/**
 * 生成限制模块模板
 */
function generateRestrictionTemplates() {
  const templates = new Map()

  // 读取所有限制模块文件
  const files = fs
    .readdirSync(RESTRICTIONS_DIR)
    .filter((file) => file.endsWith('.js') && file !== 'index.js')

  for (const file of files) {
    const filePath = path.join(RESTRICTIONS_DIR, file)
    const moduleId = path
      .basename(file, '.js')
      .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())

    const applyContent = parseModuleFile(filePath)
    if (applyContent) {
      templates.set(
        moduleId,
        `
// ${getModuleName(moduleId)}限制
${applyContent}`,
      )
    }
  }

  return templates
}

/**
 * 获取模块中文名称 - 从模块文件中动态获取
 */
function getModuleName(moduleId) {
  // 从已经读取的模块列表中获取名称
  const moduleList = getModuleListFromIndex()
  const module = moduleList.find((m) => m.id === moduleId)
  return module ? module.name : moduleId
}

/**
 * 生成 polyfills 模板
 */
function generatePolyfillsTemplate() {
  const polyfillsPath = path.join(__dirname, '../templates/polyfills.js')

  if (!fs.existsSync(polyfillsPath)) {
    console.warn('⚠️  polyfills.js 文件不存在，跳过生成')
    return '// polyfills.js 未找到'
  }

  try {
    const polyfillsContent = fs.readFileSync(polyfillsPath, 'utf-8')

    // 移除文件开头的注释块（保留代码中的注释）
    const cleanContent = polyfillsContent
      .replace(/^\/\*\*[\s\S]*?\*\/\s*/, '') // 移除开头的文档注释
      .trim()

    console.log('✅ polyfills 模板生成成功')
    return cleanContent
  } catch (error) {
    console.warn('⚠️  生成 polyfills 模板失败:', error.message)
    return '// polyfills 生成失败: ' + error.message
  }
}

/**
 * 生成资源收集器模板
 */
function generateResourceCollectorTemplate() {
  const resourceCollectorPath = path.join(__dirname, '../core/resource-collector.js')
  const utilsPath = path.join(__dirname, '../core/utils.js')

  if (!fs.existsSync(resourceCollectorPath) || !fs.existsSync(utilsPath)) {
    console.warn('⚠️  资源收集器或工具文件不存在，跳过生成')
    return '// 资源收集器未找到'
  }

  try {
    const resourceCollectorContent = fs.readFileSync(resourceCollectorPath, 'utf-8')
    const utilsContent = fs.readFileSync(utilsPath, 'utf-8')

    // 只移除 import 语句，让 Babel 处理所有其他转换
    const cleanUtilsContent = utilsContent.replace(/import.*?from.*?['"].*?['"];?\s*/g, '')

    const cleanResourceCollectorContent = resourceCollectorContent.replace(
      /import.*?from.*?['"].*?['"];?\s*/g,
      '',
    )

    // 创建完整的资源收集器模板（不包含 polyfills，因为已在 build() 方法的 start 部分添加）
    const resourceCollectorTemplate = `
${cleanUtilsContent}

${cleanResourceCollectorContent}

// 初始化资源收集器
const resourceCollector = ResourceCollector.getInstance();

// 暴露全局方法
if (typeof window !== 'undefined') {
  Object.defineProperties(window, {
    _getAllResourceUrls: {
      value: () => resourceCollector.init(),
      writable: false,
      configurable: false,
    },
    _cleanupWatch: {
      value: () => resourceCollector.cleanup(),
      writable: false,
      configurable: false,
    },
  });
}

console.log('[WebCapacity] 资源收集器已初始化');`

    // 使用 Babel 转换为 ES5 兼容代码
    console.log('🔄 正在转换资源收集器源码为 ES5...')
    return transformToES5(resourceCollectorTemplate, 'resource-collector-template')
  } catch (error) {
    console.warn('⚠️  生成资源收集器模板失败:', error.message)
    return '// 资源收集器生成失败: ' + error.message
  }
}

/**
 * 转义模板字符串中的特殊字符
 */
function escapeTemplateString(str) {
  return str
    .replace(/\\/g, '\\\\') // 转义反斜杠
    .replace(/`/g, '\\`') // 转义反引号
    .replace(/\${/g, '\\${') // 转义模板字符串插值
}

/**
 * 生成 loadModuleTemplates 方法代码
 */
function generateLoadModuleTemplatesMethod() {
  const restrictionTemplates = generateRestrictionTemplates()

  let methodCode = `  /**
   * 加载模块模板
   */
  loadModuleTemplates() {`

  // 添加所有限制模块模板
  for (const [moduleId, template] of restrictionTemplates) {
    const escapedTemplate = escapeTemplateString(template)
    methodCode += `
    this.moduleTemplates.set('${moduleId}', \`${escapedTemplate}\`)`
  }

  methodCode += `
  }`

  return methodCode
}

/**
 * 生成完整的 ModuleBuilder 文件内容
 */
function generateCompleteModuleBuilderFile() {
  const moduleList = getModuleListFromIndex()
  const restrictionTemplates = generateRestrictionTemplates()
  const resourceCollectorTemplate = generateResourceCollectorTemplate()

  // 生成 polyfills 模板
  const polyfillsTemplate = generatePolyfillsTemplate()

  // 生成模块列表代码
  const moduleListCode = moduleList
    .map(
      (module) =>
        `      { id: '${module.id}', name: '${module.name}', description: '${module.description}' }`,
    )
    .join(',\n')

  // 生成模板设置代码
  let templateSetCode = ''
  for (const [moduleId, template] of restrictionTemplates) {
    const escapedTemplate = escapeTemplateString(template)
    templateSetCode += `    this.moduleTemplates.set('${moduleId}', \`${escapedTemplate}\`)\n`
  }

  // 转义资源收集器模板
  const escapedResourceCollectorTemplate = escapeTemplateString(resourceCollectorTemplate)

  // 转义 polyfills 模板
  const escapedPolyfillsTemplate = escapeTemplateString(polyfillsTemplate)

  const templateString = `/**
 * 模块构建器 - 用于动态组装JS代码
 */

/**
 * 配置解析器
 */
export class ConfigParser {
  constructor(config) {
    this.config = config || {}
    this.resourceCollection = config.resourceCollection !== false // 默认启用
    this.enabledModules = config.enabledModules || []
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = []

    if (!Array.isArray(this.enabledModules)) {
      errors.push('enabledModules 必须是数组')
    }

    if (typeof this.resourceCollection !== 'boolean') {
      errors.push('resourceCollection 必须是布尔值')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * 获取解析后的配置
   */
  getParsedConfig() {
    return {
      resourceCollection: this.resourceCollection,
      enabledModules: this.enabledModules,
    }
  }
}

/**
 * 模块构建器
 */
export class ModuleBuilder {
  constructor(config) {
    this.parser = new ConfigParser(config)
    const validation = this.parser.validate()

    if (!validation.valid) {
      throw new Error(\`配置验证失败: \${validation.errors.join(', ')}\`)
    }

    this.config = this.parser.getParsedConfig()
    this.moduleTemplates = new Map()
    this.loadModuleTemplates()
  }

  /**
   * 获取 polyfills 代码
   */
  static getPolyfills() {
    return \`{{POLYFILLS_PLACEHOLDER}}\`;
  }

  /**
   * 加载模块模板
   */
  loadModuleTemplates() {
${templateSetCode}  }

  /**
   * 获取可用的模块列表
   */
  static getAvailableModules() {
    return [
${moduleListCode},
    ]
  }

  /**
   * 验证模块ID
   */
  static validateModuleIds(moduleIds) {
    const availableIds = this.getAvailableModules().map((m) => m.id)
    const invalidIds = moduleIds.filter((id) => !availableIds.includes(id))

    return {
      valid: invalidIds.length === 0,
      invalidIds,
    }
  }

  /**
   * 构建JS代码
   */
  build() {
    const parts = []

    // 添加IIFE包装开始
    parts.push('(function() {')
    parts.push('  "use strict";')
    parts.push('')

    // 添加 polyfills（使用静态方法）
    parts.push('  // ES5 兼容性 polyfills')
    const polyfillsTemplate = ModuleBuilder.getPolyfills()
    const polyfillsLines = polyfillsTemplate.split('\\n')
    polyfillsLines.forEach(line => {
      parts.push('  ' + line)
    })
    parts.push('')

    // 添加启用的限制模块
    for (const moduleId of this.config.enabledModules) {
      const template = this.moduleTemplates.get(moduleId)
      if (template) {
        parts.push('  // ' + moduleId + ' 模块')
        parts.push('  try {')
        parts.push(
          template
            .split('\\n')
            .map((line) => '    ' + line)
            .join('\\n'),
        )
        parts.push('  } catch (error) {')
        parts.push(\`    console.warn('[WebCapacity] \${moduleId} 模块应用失败:', error)\`)
        parts.push('  }')
        parts.push('')
      }
    }

    // 添加资源收集器（如果启用）
    if (this.config.resourceCollection) {
      parts.push('  // 资源收集器')
      parts.push('  try {')
      parts.push(\`${escapedResourceCollectorTemplate}\`.split('\\n').map(line => '    ' + line).join('\\n'))
      parts.push('  } catch (error) {')
      parts.push('    console.warn("[WebCapacity] 资源收集器启动失败:", error)')
      parts.push('  }')
      parts.push('')
    }

    // 添加IIFE包装结束
    parts.push(
      '  console.log("[WebCapacity] 初始化完成，已应用", ' +
        JSON.stringify(this.config.enabledModules) +
        ', "个限制模块")',
    )
    parts.push('})();')

    return parts.join('\\n')
  }
}
`

  // 替换 polyfills 占位符
  const finalContent = templateString.replace('{{POLYFILLS_PLACEHOLDER}}', escapedPolyfillsTemplate)

  return finalContent
}

/**
 * 更新 ModuleBuilder 文件
 */
function updateModuleBuilderFile() {
  try {
    // 生成完整的文件内容
    const newFileContent = generateCompleteModuleBuilderFile()

    // 写入文件
    fs.writeFileSync(BUILDER_FILE, newFileContent, 'utf-8')

    console.log('✅ 已生成新的 ModuleBuilder 文件')
    return true
  } catch (error) {
    console.error('❌ 生成 ModuleBuilder 文件失败:', error)
    return false
  }
}

/**
 * 生成模板映射文件
 */
function generateTemplateMapping() {
  const restrictionTemplates = generateRestrictionTemplates()

  const mapping = {
    utils: 'core/utils.js',
    resourceCollector: 'core/resource-collector.js',
  }

  for (const moduleId of restrictionTemplates.keys()) {
    const fileName = moduleId
      .replace(/([A-Z])/g, '-$1')
      .toLowerCase()
      .replace(/^-/, '')
    mapping[moduleId] = `restrictions/${fileName}.js`
  }

  const mappingFile = path.join(__dirname, '../builder/template-mapping.json')
  fs.writeFileSync(mappingFile, JSON.stringify(mapping, null, 2), 'utf-8')

  console.log('✅ 已生成模板映射文件:', mappingFile)
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🚀 开始生成 ModuleBuilder 模板...')

    // 检查必要的目录和文件
    if (!fs.existsSync(RESTRICTIONS_DIR)) {
      throw new Error(`restrictions 目录不存在: ${RESTRICTIONS_DIR}`)
    }

    // 更新 ModuleBuilder 文件
    const success = updateModuleBuilderFile()

    if (success) {
      // 生成模板映射文件
      generateTemplateMapping()

      console.log('🎉 模板生成完成！')
      console.log('')
      console.log('📋 生成的模块模板:')

      const restrictionTemplates = generateRestrictionTemplates()
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      for (const [moduleId, _] of restrictionTemplates) {
        console.log(`  - ${moduleId} (${getModuleName(moduleId)})`)
      }

      console.log('')
      console.log('💡 现在你只需要维护 restrictions 目录下的模块文件，')
      console.log('   ModuleBuilder 的模板会自动同步！')
      console.log('')
      console.log('🔄 重新构建项目以应用更改:')
      console.log('   npm run build:web-capacity-all')
    }
  } catch (error) {
    console.error('❌ 生成失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { generateLoadModuleTemplatesMethod, updateModuleBuilderFile, generateTemplateMapping }
