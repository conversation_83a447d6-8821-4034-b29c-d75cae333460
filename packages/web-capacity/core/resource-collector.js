/**
 * 资源收集器模块
 */
import { Utils } from './utils.js'

export class ResourceCollector {
  static instance = null

  static IMGTYPE = Object.freeze({
    JPG: 'jpg',
    JPEG: 'jpeg',
    PNG: 'png',
    GIF: 'gif',
    WEBP: 'webp',
    TIFF: 'tiff',
    SVG: 'svg',
    BASE64: 'base64',
  })

  static SELECTOR_MAP = new Map([
    [
      'img',
      {
        type: 'images',
        attr: 'src',
        processFunc: (element, attr) => {
          const src = element[attr]
          const type = ResourceCollector.getImageType(src)
          if (src) {
            return {
              type,
              src: type !== ResourceCollector.IMGTYPE.BASE64 ? src : '',
            }
          }
          return null
        },
      },
    ],
    ['source[type^="image"]', { type: 'images', attr: 'srcset' }],
    ['picture source', { type: 'images', attr: 'srcset' }],
    ['video', { type: 'videos', attr: 'src', hasSource: true }],
    ['audio', { type: 'audios', attr: 'src', hasSource: true }],
    ['link[rel="stylesheet"]', { type: 'stylesheets', attr: 'href' }],
    ['script', { type: 'scripts', attr: 'src' }],
    ['iframe', { type: 'iframes', attr: 'src' }],
  ])

  constructor() {
    this.store = {}
    this.observer = null
    this.storeEntries = []
    this._originalHistoryFns = new Map()

    this.handleResourceUpdate = Utils.debounce(() => {
      this.storeEntries.forEach(([, set]) => set.clear())
      this.notifyApp(this.collect())
    }, 500)

    this.handleUrlChange = Utils.debounce(() => {
      this.handleResourceUpdate()
      this.notifyAppUrlChange(window.location.href)
    }, 500)
  }

  static getInstance() {
    if (!ResourceCollector.instance) {
      ResourceCollector.instance = new ResourceCollector()
    }
    return ResourceCollector.instance
  }

  init() {
    this.store = Object.fromEntries(
      Array.from(ResourceCollector.SELECTOR_MAP.values(), ({ type }) => [type, new Set()]),
    )
    this.storeEntries = Object.entries(this.store)
    this.startWatching()
  }

  static getImageType(src) {
    try {
      if (!src || typeof src !== 'string') return 'unknown'
      if (src.startsWith('data:image/')) return this.IMGTYPE.BASE64
      const urlWithoutParams = src.split('?')[0].split('#')[0]
      const extension = urlWithoutParams.split('.').pop().toLowerCase()
      return Object.values(this.IMGTYPE).includes(extension) ? extension : 'unknown'
    } catch {
      return 'unknown'
    }
  }

  collectBySelector(selector, config) {
    const elements = document.querySelectorAll(selector)
    if (!elements.length) return

    const { type, attr, processFunc, hasSource } = config
    const resourceSet = this.store[type]

    for (const element of elements) {
      if (processFunc) {
        const result = processFunc(element, attr)
        if (result) {
          resourceSet.add(JSON.stringify(result))
        }
        continue
      }

      const attrValue = element[attr]
      if (attrValue) {
        if (selector === 'iframe') {
          this.pauseObserver()
          element[attr] = ''
          this.resumeObserver()
        } else {
          resourceSet.add(attrValue)
        }
      }

      if (hasSource) {
        element.querySelectorAll('source').forEach((source) => {
          const sourceSrc = source.src
          if (sourceSrc) resourceSet.add(sourceSrc)
        })
      }
    }
  }

  collect() {
    ResourceCollector.SELECTOR_MAP.forEach((config, selector) =>
      this.collectBySelector(selector, config),
    )
    const data = Object.fromEntries(
      this.storeEntries.map(([key, set]) => [
        key,
        Array.from(set, (item) => {
          try {
            return JSON.parse(item)
          } catch {
            return item
          }
        }),
      ]),
    )
    delete data.iframes
    return Object.freeze(data)
  }

  startWatching() {
    if (this.observer) {
      this.cleanup()
    }

    this.notifyApp(this.collect())
    // ios hash模式的url时，startWatching时通知一次，非hash不通知，安卓不管什么模式都通知
    if (Utils.isAndroid()) {
      this.notifyAppUrlChange(window.location.href)
    } else if (Utils.isIOS() && Utils.isHashMode()) {
      this.notifyAppUrlChange(window.location.href)
    }

    this.observer = this.createResourceWatcher()

    if (this.observer) {
      this.observer.observe(document.documentElement, {
        subtree: true,
        childList: true,
        attributes: true,
        attributeFilter: ['src', 'href'],
      })
    }

    this.routePatch()
    window.addEventListener('unload', this.cleanup, { once: true })
  }

  routePatch() {
    if (history.__patched) return
    history.__patched = true
    const handleUrlChange = this.handleUrlChange.bind(this)
    const patchHistoryMethod = Utils.patchHistoryMethod.bind(this)
    patchHistoryMethod('pushState', handleUrlChange)
    patchHistoryMethod('replaceState', handleUrlChange)
    patchHistoryMethod('back', handleUrlChange)
    patchHistoryMethod('forward', handleUrlChange)
    patchHistoryMethod('go', handleUrlChange)
  }

  notifyApp(urls) {
    if (!urls || typeof urls !== 'object') return
    const data = JSON.stringify(urls)
    try {
      window?.webkit?.messageHandlers?.resourceBridge?.postMessage(data)
      window?.resourceBridge?.resourceUpdate(data)
    } catch (error) {
      console.warn('发送资源列表到 App 失败:', error)
    }
  }

  notifyAppUrlChange(url) {
    if (!url || typeof url !== 'string') return
    try {
      window?.webkit?.messageHandlers?.urlChangeBridge?.postMessage(url)
      window?.urlChangeBridge?.urlUpdate(url)
    } catch (error) {
      console.warn('发送资源列表到 App 失败:', error)
    }
  }

  createResourceWatcher() {
    const SafeMutationObserver = Object.freeze(window.MutationObserver)
    if (typeof SafeMutationObserver === 'function') {
      return new SafeMutationObserver(this.handleResourceUpdate.bind(this))
    }
    return null
  }

  cleanup() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    Utils.restoreHistoryPatch()
    Object.keys(this.store).forEach((key) => this.store[key].clear())
  }

  pauseObserver() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  resumeObserver() {
    if (this.observer) {
      this.observer.observe(document.documentElement, {
        subtree: true,
        childList: true,
        attributes: true,
        attributeFilter: ['src', 'href'],
      })
    }
  }
}
