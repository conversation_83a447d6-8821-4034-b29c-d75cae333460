/**
 * 核心工具函数模块
 */
export const Utils = {
  debounce(fn, delay, immediate = false) {
    let timer = null
    return function (...args) {
      clearTimeout(timer)
      const isImmediate = immediate && !timer
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
      if (isImmediate) {
        fn.apply(this, args)
      }
    }
  },

  patchHistoryMethod(methodName, callback) {
    const original = history[methodName]
    if (!this._originalHistoryFns.has(methodName)) {
      this._originalHistoryFns.set(methodName, original)
    }

    let current = original

    Object.defineProperty(history, methodName, {
      configurable: true,
      enumerable: true,
      get() {
        return function (...args) {
          callback()
          return current.apply(this, args)
        }
      },
      set(newFn) {
        console.warn(`[patch] ${methodName} 被重写，已再次包裹`)
        current = function (...args) {
          callback()
          return newFn.apply(this, args)
        }
      },
    })
  },

  restoreHistoryPatch() {
    this._originalHistoryFns.forEach((originalFn, methodName) => {
      Object.defineProperty(history, methodName, {
        configurable: true,
        enumerable: true,
        writable: true,
        value: originalFn,
      })
    })
    this._originalHistoryFns.clear()
  },

  isHashMode() {
    return location.href.includes('#') && !location.pathname.includes('/#/')
  },

  isAndroid() {
    return /Android/i.test(navigator.userAgent)
  },

  isIOS() {
    const ua = navigator.userAgent
    const platform = navigator.platform
    const isIOSDevice = /iP(hone|od|ad)/.test(ua)
    const isIpadOS13 = platform === 'MacIntel' && navigator.maxTouchPoints > 1
    return isIOSDevice || isIpadOS13
  },

  // 初始化历史记录函数映射
  _originalHistoryFns: new Map(),
}
