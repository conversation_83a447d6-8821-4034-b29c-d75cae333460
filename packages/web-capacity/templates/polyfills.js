// Object.assign
if (typeof Object.assign !== 'function') {
  Object.assign = function (target, varArgs) {
    if (target == null) throw new TypeError('Cannot convert undefined or null to object')
    var to = Object(target)
    for (var index = 1; index < arguments.length; index++) {
      var nextSource = arguments[index]
      if (nextSource != null) {
        for (var nextKey in nextSource) {
          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
            to[nextKey] = nextSource[nextKey]
          }
        }
      }
    }
    return to
  }
}

// Object.entries
if (!Object.entries) {
  Object.entries = function (obj) {
    var ownProps = Object.keys(obj)
    var i = ownProps.length
    var resArray = new Array(i)
    while (i--) {
      resArray[i] = [ownProps[i], obj[ownProps[i]]]
    }
    return resArray
  }
}

// Object.fromEntries
if (!Object.fromEntries) {
  Object.fromEntries = function (entries) {
    if (!entries || typeof entries[Symbol.iterator] !== 'function') {
      throw new Error('Object.fromEntries() requires a single iterable argument')
    }
    var obj = {}
    for (var pair of entries) {
      if (Object(pair) !== pair) {
        throw new TypeError('iterable for fromEntries should yield objects')
      }
      var key = pair[0]
      var val = pair[1]
      obj[key] = val
    }
    return obj
  }
}

// Array.from
if (!Array.from) {
  Array.from = (function () {
    var toStr = Object.prototype.toString
    var isCallable = function (fn) {
      return typeof fn === 'function'
    }
    var toInteger = function (value) {
      var number = Number(value)
      if (isNaN(number)) return 0
      if (number === 0 || !isFinite(number)) return number
      return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number))
    }
    var maxSafeInteger = Math.pow(2, 53) - 1
    var toLength = function (value) {
      var len = toInteger(value)
      return Math.min(Math.max(len, 0), maxSafeInteger)
    }

    return function from(arrayLike) {
      var C = this
      var items = Object(arrayLike)
      if (arrayLike == null) throw new TypeError('Array.from requires an array-like object')
      var len = toLength(items.length)
      var A = isCallable(C) ? Object(new C(len)) : new Array(len)
      for (var k = 0; k < len; k++) {
        A[k] = items[k]
      }
      A.length = len
      return A
    }
  })()
}

// Array.prototype.includes
if (!Array.prototype.includes) {
  Array.prototype.includes = function (valueToFind, fromIndex) {
    if (this == null) throw new TypeError('"this" is null or not defined')
    var o = Object(this)
    var len = o.length >>> 0
    if (len === 0) return false
    var n = fromIndex | 0
    var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0)
    while (k < len) {
      if (
        o[k] === valueToFind ||
        (typeof o[k] === 'number' &&
          typeof valueToFind === 'number' &&
          isNaN(o[k]) &&
          isNaN(valueToFind))
      ) {
        return true
      }
      k++
    }
    return false
  }
}

// String.prototype.includes / startsWith / endsWith / trim
if (!String.prototype.includes) {
  String.prototype.includes = function (search, start) {
    if (typeof start !== 'number') start = 0
    if (start + search.length > this.length) return false
    return this.indexOf(search, start) !== -1
  }
}

if (!String.prototype.startsWith) {
  String.prototype.startsWith = function (search, pos) {
    pos = pos || 0
    return this.substr(pos, search.length) === search
  }
}

if (!String.prototype.endsWith) {
  String.prototype.endsWith = function (search, this_len) {
    if (this_len === undefined || this_len > this.length) this_len = this.length
    return this.substring(this_len - search.length, this_len) === search
  }
}

if (!String.prototype.trim) {
  String.prototype.trim = function () {
    return this.replace(/^\s+|\s+$/g, '')
  }
}

// Map (简易版) - 适用于 template.json 环境
if (typeof Map === 'undefined') {
  window.Map = function () {
    this._keys = []
    this._values = []
  }
  Map.prototype.set = function (k, v) {
    var i = this._keys.indexOf(k)
    if (i === -1) {
      this._keys.push(k)
      this._values.push(v)
    } else {
      this._values[i] = v
    }
    return this
  }
  Map.prototype.get = function (k) {
    var i = this._keys.indexOf(k)
    return i !== -1 ? this._values[i] : undefined
  }
  Map.prototype.has = function (k) {
    return this._keys.indexOf(k) !== -1
  }
  Map.prototype.delete = function (k) {
    var i = this._keys.indexOf(k)
    if (i !== -1) {
      this._keys.splice(i, 1)
      this._values.splice(i, 1)
      return true
    }
    return false
  }
  Map.prototype.clear = function () {
    this._keys = []
    this._values = []
  }
  Map.prototype.forEach = function (callback, thisArg) {
    for (var i = 0; i < this._keys.length; i++) {
      callback.call(thisArg, this._values[i], this._keys[i], this)
    }
  }
}

// Set (简易版) - 同上
if (typeof Set === 'undefined') {
  window.Set = function () {
    this._items = []
  }
  Set.prototype.add = function (value) {
    if (this._items.indexOf(value) === -1) this._items.push(value)
    return this
  }
  Set.prototype.has = function (value) {
    return this._items.indexOf(value) !== -1
  }
  Set.prototype.delete = function (value) {
    var i = this._items.indexOf(value)
    if (i !== -1) {
      this._items.splice(i, 1)
      return true
    }
    return false
  }
  Set.prototype.clear = function () {
    this._items = []
  }
  Set.prototype.forEach = function (callback, thisArg) {
    for (var i = 0; i < this._items.length; i++) {
      callback.call(thisArg, this._items[i], this._items[i], this)
    }
  }
}
