/**
 * 模块构建器 - 用于动态组装JS代码
 */

/**
 * 配置解析器
 */
export class ConfigParser {
  constructor(config) {
    this.config = config || {}
    this.resourceCollection = config.resourceCollection !== false // 默认启用
    this.enabledModules = config.enabledModules || []
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = []

    if (!Array.isArray(this.enabledModules)) {
      errors.push('enabledModules 必须是数组')
    }

    if (typeof this.resourceCollection !== 'boolean') {
      errors.push('resourceCollection 必须是布尔值')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * 获取解析后的配置
   */
  getParsedConfig() {
    return {
      resourceCollection: this.resourceCollection,
      enabledModules: this.enabledModules,
    }
  }
}

/**
 * 模块构建器
 */
export class ModuleBuilder {
  constructor(config) {
    this.parser = new ConfigParser(config)
    const validation = this.parser.validate()

    if (!validation.valid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
    }

    this.config = this.parser.getParsedConfig()
    this.moduleTemplates = new Map()
    this.loadModuleTemplates()
  }

  /**
   * 获取 polyfills 代码
   */
  static getPolyfills() {
    return `// Object.assign
if (typeof Object.assign !== 'function') {
  Object.assign = function (target, varArgs) {
    if (target == null) throw new TypeError('Cannot convert undefined or null to object')
    var to = Object(target)
    for (var index = 1; index < arguments.length; index++) {
      var nextSource = arguments[index]
      if (nextSource != null) {
        for (var nextKey in nextSource) {
          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
            to[nextKey] = nextSource[nextKey]
          }
        }
      }
    }
    return to
  }
}

// Object.entries
if (!Object.entries) {
  Object.entries = function (obj) {
    var ownProps = Object.keys(obj)
    var i = ownProps.length
    var resArray = new Array(i)
    while (i--) {
      resArray[i] = [ownProps[i], obj[ownProps[i]]]
    }
    return resArray
  }
}

// Object.fromEntries
if (!Object.fromEntries) {
  Object.fromEntries = function (entries) {
    if (!entries || typeof entries[Symbol.iterator] !== 'function') {
      throw new Error('Object.fromEntries() requires a single iterable argument')
    }
    var obj = {}
    for (var pair of entries) {
      if (Object(pair) !== pair) {
        throw new TypeError('iterable for fromEntries should yield objects')
      }
      var key = pair[0]
      var val = pair[1]
      obj[key] = val
    }
    return obj
  }
}

// Array.from
if (!Array.from) {
  Array.from = (function () {
    var toStr = Object.prototype.toString
    var isCallable = function (fn) {
      return typeof fn === 'function'
    }
    var toInteger = function (value) {
      var number = Number(value)
      if (isNaN(number)) return 0
      if (number === 0 || !isFinite(number)) return number
      return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number))
    }
    var maxSafeInteger = Math.pow(2, 53) - 1
    var toLength = function (value) {
      var len = toInteger(value)
      return Math.min(Math.max(len, 0), maxSafeInteger)
    }

    return function from(arrayLike) {
      var C = this
      var items = Object(arrayLike)
      if (arrayLike == null) throw new TypeError('Array.from requires an array-like object')
      var len = toLength(items.length)
      var A = isCallable(C) ? Object(new C(len)) : new Array(len)
      for (var k = 0; k < len; k++) {
        A[k] = items[k]
      }
      A.length = len
      return A
    }
  })()
}

// Array.prototype.includes
if (!Array.prototype.includes) {
  Array.prototype.includes = function (valueToFind, fromIndex) {
    if (this == null) throw new TypeError('"this" is null or not defined')
    var o = Object(this)
    var len = o.length >>> 0
    if (len === 0) return false
    var n = fromIndex | 0
    var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0)
    while (k < len) {
      if (
        o[k] === valueToFind ||
        (typeof o[k] === 'number' &&
          typeof valueToFind === 'number' &&
          isNaN(o[k]) &&
          isNaN(valueToFind))
      ) {
        return true
      }
      k++
    }
    return false
  }
}

// String.prototype.includes / startsWith / endsWith / trim
if (!String.prototype.includes) {
  String.prototype.includes = function (search, start) {
    if (typeof start !== 'number') start = 0
    if (start + search.length > this.length) return false
    return this.indexOf(search, start) !== -1
  }
}

if (!String.prototype.startsWith) {
  String.prototype.startsWith = function (search, pos) {
    pos = pos || 0
    return this.substr(pos, search.length) === search
  }
}

if (!String.prototype.endsWith) {
  String.prototype.endsWith = function (search, this_len) {
    if (this_len === undefined || this_len > this.length) this_len = this.length
    return this.substring(this_len - search.length, this_len) === search
  }
}

if (!String.prototype.trim) {
  String.prototype.trim = function () {
    return this.replace(/^\\s+|\\s+$/g, '')
  }
}

// Map (简易版) - 适用于 template.json 环境
if (typeof Map === 'undefined') {
  window.Map = function () {
    this._keys = []
    this._values = []
  }
  Map.prototype.set = function (k, v) {
    var i = this._keys.indexOf(k)
    if (i === -1) {
      this._keys.push(k)
      this._values.push(v)
    } else {
      this._values[i] = v
    }
    return this
  }
  Map.prototype.get = function (k) {
    var i = this._keys.indexOf(k)
    return i !== -1 ? this._values[i] : undefined
  }
  Map.prototype.has = function (k) {
    return this._keys.indexOf(k) !== -1
  }
  Map.prototype.delete = function (k) {
    var i = this._keys.indexOf(k)
    if (i !== -1) {
      this._keys.splice(i, 1)
      this._values.splice(i, 1)
      return true
    }
    return false
  }
  Map.prototype.clear = function () {
    this._keys = []
    this._values = []
  }
  Map.prototype.forEach = function (callback, thisArg) {
    for (var i = 0; i < this._keys.length; i++) {
      callback.call(thisArg, this._values[i], this._keys[i], this)
    }
  }
}

// Set (简易版) - 同上
if (typeof Set === 'undefined') {
  window.Set = function () {
    this._items = []
  }
  Set.prototype.add = function (value) {
    if (this._items.indexOf(value) === -1) this._items.push(value)
    return this
  }
  Set.prototype.has = function (value) {
    return this._items.indexOf(value) !== -1
  }
  Set.prototype.delete = function (value) {
    var i = this._items.indexOf(value)
    if (i !== -1) {
      this._items.splice(i, 1)
      return true
    }
    return false
  }
  Set.prototype.clear = function () {
    this._items = []
  }
  Set.prototype.forEach = function (callback, thisArg) {
    for (var i = 0; i < this._items.length; i++) {
      callback.call(thisArg, this._items[i], this._items[i], this)
    }
  }
}`
  }

  /**
   * 加载模块模板
   */
  loadModuleTemplates() {
    this.moduleTemplates.set(
      'bluetooth',
      `
// 蓝牙限制
if (window.navigator && window.navigator.bluetooth) {
  Object.defineProperty(window.navigator.bluetooth, 'requestDevice', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'clipboard',
      `
// 剪贴板限制
if (window.navigator && window.navigator.clipboard) {
  Object.defineProperty(window.navigator.clipboard, 'readText', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
  Object.defineProperty(window.navigator.clipboard, 'writeText', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'credentials',
      `
// 凭证限制
if (window.navigator && window.navigator.credentials) {
  Object.defineProperty(window.navigator.credentials, 'get', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'deviceMotion',
      `
// 设备运动限制
if (window.DeviceMotionEvent) {
  Object.defineProperty(window.DeviceMotionEvent, 'requestPermission', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'deviceOrientation',
      `
// 设备方向限制
if (window.DeviceOrientationEvent) {
  Object.defineProperty(window.DeviceOrientationEvent, 'requestPermission', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'geolocation',
      `
// 地理位置限制
if (window.navigator && window.navigator.geolocation) {
  Object.defineProperty(window.navigator.geolocation, 'getCurrentPosition', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
  Object.defineProperty(window.navigator.geolocation, 'watchPosition', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'mediaDevices',
      `
// 媒体设备限制
if (window.navigator && window.navigator.mediaDevices) {
  Object.defineProperty(window.navigator.mediaDevices, 'getUserMedia', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
  Object.defineProperty(window.navigator.mediaDevices, 'enumerateDevices', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
  Object.defineProperty(window.navigator.mediaDevices, 'getDisplayMedia', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'mediaElement',
      `
// 媒体元素限制
if (window.HTMLMediaElement && window.HTMLMediaElement.prototype) {
  Object.defineProperty(window.HTMLMediaElement.prototype, 'play', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
  Object.defineProperty(window.HTMLMediaElement.prototype, 'requestPictureInPicture', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'network',
      `
// 网络状态限制
if (window.navigator && window.navigator.connection) {
  Object.defineProperty(window.navigator.connection, 'get', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'notification',
      `
// 通知限制
if (window.Notification) {
  Object.defineProperty(window.Notification, 'requestPermission', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'permissions',
      `
// 权限查询限制
if (window.navigator && window.navigator.permissions) {
  Object.defineProperty(window.navigator.permissions, 'query', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'print',
      `
// 打印限制
if (window.print) {
  Object.defineProperty(window, 'print', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'share',
      `
// 分享限制
if (window.navigator && window.navigator.share) {
  Object.defineProperty(window.navigator, 'share', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
    this.moduleTemplates.set(
      'storage',
      `
// 持久化存储限制
if (window.navigator && window.navigator.storage) {
  Object.defineProperty(window.navigator.storage, 'persist', {
    value: function value() {
      throw new Error('请使用app的相关功能');
    },
    writable: false,
    configurable: false,
    enumerable: true
  });
}`,
    )
  }

  /**
   * 获取可用的模块列表
   */
  static getAvailableModules() {
    return [
      { id: 'bluetooth', name: '蓝牙', description: '限制蓝牙相关API' },
      { id: 'clipboard', name: '剪贴板', description: '限制剪贴板相关API' },
      { id: 'credentials', name: '凭证', description: '限制凭证相关API' },
      { id: 'deviceMotion', name: '设备运动', description: '限制设备运动相关API' },
      { id: 'deviceOrientation', name: '设备方向', description: '限制设备方向相关API' },
      { id: 'geolocation', name: '地理位置', description: '限制地理位置相关API' },
      { id: 'mediaDevices', name: '媒体设备', description: '限制摄像头、麦克风等媒体设备相关API' },
      { id: 'mediaElement', name: '媒体元素', description: '限制HTML媒体元素相关API' },
      { id: 'network', name: '网络状态', description: '限制网络状态查询相关API' },
      { id: 'notification', name: '通知', description: '限制通知相关API' },
      { id: 'permissions', name: '权限查询', description: '限制权限查询相关API' },
      { id: 'print', name: '打印', description: '限制打印相关API' },
      { id: 'share', name: '分享', description: '限制分享相关API' },
      { id: 'storage', name: '持久化存储', description: '限制持久化存储相关API' },
    ]
  }

  /**
   * 验证模块ID
   */
  static validateModuleIds(moduleIds) {
    const availableIds = this.getAvailableModules().map((m) => m.id)
    const invalidIds = moduleIds.filter((id) => !availableIds.includes(id))

    return {
      valid: invalidIds.length === 0,
      invalidIds,
    }
  }

  /**
   * 构建JS代码
   */
  build() {
    const parts = []

    // 添加IIFE包装开始
    parts.push('(function() {')
    parts.push('  "use strict";')
    parts.push('')

    // 添加 polyfills（使用静态方法）
    parts.push('  // ES5 兼容性 polyfills')
    const polyfillsTemplate = ModuleBuilder.getPolyfills()
    const polyfillsLines = polyfillsTemplate.split('\n')
    polyfillsLines.forEach((line) => {
      parts.push('  ' + line)
    })
    parts.push('')

    // 添加启用的限制模块
    for (const moduleId of this.config.enabledModules) {
      const template = this.moduleTemplates.get(moduleId)
      if (template) {
        parts.push('  // ' + moduleId + ' 模块')
        parts.push('  try {')
        parts.push(
          template
            .split('\n')
            .map((line) => '    ' + line)
            .join('\n'),
        )
        parts.push('  } catch (error) {')
        parts.push(`    console.warn('[WebCapacity] ${moduleId} 模块应用失败:', error)`)
        parts.push('  }')
        parts.push('')
      }
    }

    // 添加资源收集器（如果启用）
    if (this.config.resourceCollection) {
      parts.push('  // 资源收集器')
      parts.push('  try {')
      parts.push(
        `var _ResourceCollector;
function _createForOfIteratorHelperLoose(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (t) return (t = t.call(r)).next.bind(t); if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var o = 0; return function () { return o >= r.length ? { done: !0 } : { done: !1, value: r[o++] }; }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/**
 * 核心工具函数模块
 */
var Utils = {
  debounce: function debounce(fn, delay, immediate) {
    if (immediate === void 0) {
      immediate = false;
    }
    var timer = null;
    return function () {
      var _this = this;
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      clearTimeout(timer);
      var isImmediate = immediate && !timer;
      timer = setTimeout(function () {
        fn.apply(_this, args);
      }, delay);
      if (isImmediate) {
        fn.apply(this, args);
      }
    };
  },
  patchHistoryMethod: function patchHistoryMethod(methodName, callback) {
    var original = history[methodName];
    if (!this._originalHistoryFns.has(methodName)) {
      this._originalHistoryFns.set(methodName, original);
    }
    var current = original;
    Object.defineProperty(history, methodName, {
      configurable: true,
      enumerable: true,
      get: function get() {
        return function () {
          callback();
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          return current.apply(this, args);
        };
      },
      set: function set(newFn) {
        console.warn("[patch] " + methodName + " \\u88AB\\u91CD\\u5199\\uFF0C\\u5DF2\\u518D\\u6B21\\u5305\\u88F9");
        current = function current() {
          callback();
          for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
            args[_key3] = arguments[_key3];
          }
          return newFn.apply(this, args);
        };
      }
    });
  },
  restoreHistoryPatch: function restoreHistoryPatch() {
    this._originalHistoryFns.forEach(function (originalFn, methodName) {
      Object.defineProperty(history, methodName, {
        configurable: true,
        enumerable: true,
        writable: true,
        value: originalFn
      });
    });
    this._originalHistoryFns.clear();
  },
  isHashMode: function isHashMode() {
    return location.href.includes('#') && !location.pathname.includes('/#/');
  },
  isAndroid: function isAndroid() {
    return /Android/i.test(navigator.userAgent);
  },
  isIOS: function isIOS() {
    var ua = navigator.userAgent;
    var platform = navigator.platform;
    var isIOSDevice = /iP(hone|od|ad)/.test(ua);
    var isIpadOS13 = platform === 'MacIntel' && navigator.maxTouchPoints > 1;
    return isIOSDevice || isIpadOS13;
  },
  // 初始化历史记录函数映射
  _originalHistoryFns: new Map()
};

/**
 * 资源收集器模块
 */
var ResourceCollector = function () {
  function ResourceCollector() {
    var _this2 = this;
    this.store = {};
    this.observer = null;
    this.storeEntries = [];
    this._originalHistoryFns = new Map();
    this.handleResourceUpdate = Utils.debounce(function () {
      _this2.storeEntries.forEach(function (_ref) {
        var set = _ref[1];
        return set.clear();
      });
      _this2.notifyApp(_this2.collect());
    }, 500);
    this.handleUrlChange = Utils.debounce(function () {
      _this2.handleResourceUpdate();
      _this2.notifyAppUrlChange(window.location.href);
    }, 500);
  }
  ResourceCollector.getInstance = function getInstance() {
    if (!ResourceCollector.instance) {
      ResourceCollector.instance = new ResourceCollector();
    }
    return ResourceCollector.instance;
  };
  var _proto = ResourceCollector.prototype;
  _proto.init = function init() {
    this.store = Object.fromEntries(Array.from(ResourceCollector.SELECTOR_MAP.values(), function (_ref2) {
      var type = _ref2.type;
      return [type, new Set()];
    }));
    this.storeEntries = Object.entries(this.store);
    this.startWatching();
  };
  ResourceCollector.getImageType = function getImageType(src) {
    try {
      if (!src || typeof src !== 'string') return 'unknown';
      if (src.startsWith('data:image/')) return this.IMGTYPE.BASE64;
      var urlWithoutParams = src.split('?')[0].split('#')[0];
      var extension = urlWithoutParams.split('.').pop().toLowerCase();
      return Object.values(this.IMGTYPE).includes(extension) ? extension : 'unknown';
    } catch (_unused) {
      return 'unknown';
    }
  };
  _proto.collectBySelector = function collectBySelector(selector, config) {
    var elements = document.querySelectorAll(selector);
    if (!elements.length) return;
    var type = config.type,
      attr = config.attr,
      processFunc = config.processFunc,
      hasSource = config.hasSource;
    var resourceSet = this.store[type];
    for (var _iterator = _createForOfIteratorHelperLoose(elements), _step; !(_step = _iterator()).done;) {
      var element = _step.value;
      if (processFunc) {
        var result = processFunc(element, attr);
        if (result) {
          resourceSet.add(JSON.stringify(result));
        }
        continue;
      }
      var attrValue = element[attr];
      if (attrValue) {
        if (selector === 'iframe') {
          this.pauseObserver();
          element[attr] = '';
          this.resumeObserver();
        } else {
          resourceSet.add(attrValue);
        }
      }
      if (hasSource) {
        element.querySelectorAll('source').forEach(function (source) {
          var sourceSrc = source.src;
          if (sourceSrc) resourceSet.add(sourceSrc);
        });
      }
    }
  };
  _proto.collect = function collect() {
    var _this3 = this;
    ResourceCollector.SELECTOR_MAP.forEach(function (config, selector) {
      return _this3.collectBySelector(selector, config);
    });
    var data = Object.fromEntries(this.storeEntries.map(function (_ref3) {
      var key = _ref3[0],
        set = _ref3[1];
      return [key, Array.from(set, function (item) {
        try {
          return JSON.parse(item);
        } catch (_unused2) {
          return item;
        }
      })];
    }));
    delete data.iframes;
    return Object.freeze(data);
  };
  _proto.startWatching = function startWatching() {
    if (this.observer) {
      this.cleanup();
    }
    this.notifyApp(this.collect());
    // ios hash模式的url时，startWatching时通知一次，非hash不通知，安卓不管什么模式都通知
    if (Utils.isAndroid()) {
      this.notifyAppUrlChange(window.location.href);
    } else if (Utils.isIOS() && Utils.isHashMode()) {
      this.notifyAppUrlChange(window.location.href);
    }
    this.observer = this.createResourceWatcher();
    if (this.observer) {
      this.observer.observe(document.documentElement, {
        subtree: true,
        childList: true,
        attributes: true,
        attributeFilter: ['src', 'href']
      });
    }
    this.routePatch();
    window.addEventListener('unload', this.cleanup, {
      once: true
    });
  };
  _proto.routePatch = function routePatch() {
    if (history.__patched) return;
    history.__patched = true;
    var handleUrlChange = this.handleUrlChange.bind(this);
    var patchHistoryMethod = Utils.patchHistoryMethod.bind(this);
    patchHistoryMethod('pushState', handleUrlChange);
    patchHistoryMethod('replaceState', handleUrlChange);
    patchHistoryMethod('back', handleUrlChange);
    patchHistoryMethod('forward', handleUrlChange);
    patchHistoryMethod('go', handleUrlChange);
  };
  _proto.notifyApp = function notifyApp(urls) {
    if (!urls || typeof urls !== 'object') return;
    var data = JSON.stringify(urls);
    try {
      var _window, _window2;
      (_window = window) == null || (_window = _window.webkit) == null || (_window = _window.messageHandlers) == null || (_window = _window.resourceBridge) == null || _window.postMessage(data);
      (_window2 = window) == null || (_window2 = _window2.resourceBridge) == null || _window2.resourceUpdate(data);
    } catch (error) {
      console.warn('发送资源列表到 App 失败:', error);
    }
  };
  _proto.notifyAppUrlChange = function notifyAppUrlChange(url) {
    if (!url || typeof url !== 'string') return;
    try {
      var _window3, _window4;
      (_window3 = window) == null || (_window3 = _window3.webkit) == null || (_window3 = _window3.messageHandlers) == null || (_window3 = _window3.urlChangeBridge) == null || _window3.postMessage(url);
      (_window4 = window) == null || (_window4 = _window4.urlChangeBridge) == null || _window4.urlUpdate(url);
    } catch (error) {
      console.warn('发送资源列表到 App 失败:', error);
    }
  };
  _proto.createResourceWatcher = function createResourceWatcher() {
    var SafeMutationObserver = Object.freeze(window.MutationObserver);
    if (typeof SafeMutationObserver === 'function') {
      return new SafeMutationObserver(this.handleResourceUpdate.bind(this));
    }
    return null;
  };
  _proto.cleanup = function cleanup() {
    var _this4 = this;
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    Utils.restoreHistoryPatch();
    Object.keys(this.store).forEach(function (key) {
      return _this4.store[key].clear();
    });
  };
  _proto.pauseObserver = function pauseObserver() {
    if (this.observer) {
      this.observer.disconnect();
    }
  };
  _proto.resumeObserver = function resumeObserver() {
    if (this.observer) {
      this.observer.observe(document.documentElement, {
        subtree: true,
        childList: true,
        attributes: true,
        attributeFilter: ['src', 'href']
      });
    }
  };
  return ResourceCollector;
}();

// 初始化资源收集器
_ResourceCollector = ResourceCollector;
ResourceCollector.instance = null;
ResourceCollector.IMGTYPE = Object.freeze({
  JPG: 'jpg',
  JPEG: 'jpeg',
  PNG: 'png',
  GIF: 'gif',
  WEBP: 'webp',
  TIFF: 'tiff',
  SVG: 'svg',
  BASE64: 'base64'
});
ResourceCollector.SELECTOR_MAP = new Map([['img', {
  type: 'images',
  attr: 'src',
  processFunc: function processFunc(element, attr) {
    var src = element[attr];
    var type = _ResourceCollector.getImageType(src);
    if (src) {
      return {
        type: type,
        src: type !== _ResourceCollector.IMGTYPE.BASE64 ? src : ''
      };
    }
    return null;
  }
}], ['source[type^="image"]', {
  type: 'images',
  attr: 'srcset'
}], ['picture source', {
  type: 'images',
  attr: 'srcset'
}], ['video', {
  type: 'videos',
  attr: 'src',
  hasSource: true
}], ['audio', {
  type: 'audios',
  attr: 'src',
  hasSource: true
}], ['link[rel="stylesheet"]', {
  type: 'stylesheets',
  attr: 'href'
}], ['script', {
  type: 'scripts',
  attr: 'src'
}], ['iframe', {
  type: 'iframes',
  attr: 'src'
}]]);
var resourceCollector = ResourceCollector.getInstance();

// 暴露全局方法
if (typeof window !== 'undefined') {
  Object.defineProperties(window, {
    _getAllResourceUrls: {
      value: function value() {
        return resourceCollector.init();
      },
      writable: false,
      configurable: false
    },
    _cleanupWatch: {
      value: function value() {
        return resourceCollector.cleanup();
      },
      writable: false,
      configurable: false
    }
  });
}
console.log('[WebCapacity] 资源收集器已初始化');`
          .split('\n')
          .map((line) => '    ' + line)
          .join('\n'),
      )
      parts.push('  } catch (error) {')
      parts.push('    console.warn("[WebCapacity] 资源收集器启动失败:", error)')
      parts.push('  }')
      parts.push('')
    }

    // 添加IIFE包装结束
    parts.push(
      '  console.log("[WebCapacity] 初始化完成，已应用", ' +
        JSON.stringify(this.config.enabledModules) +
        ', "个限制模块")',
    )
    parts.push('})();')

    return parts.join('\n')
  }
}
