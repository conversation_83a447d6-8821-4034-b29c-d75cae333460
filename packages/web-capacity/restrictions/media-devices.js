/**
 * 媒体设备限制模块
 */
export const mediaDevices = {
  id: 'mediaDevices',
  name: '媒体设备',
  description: '限制摄像头、麦克风等媒体设备相关API',
  target: 'navigator.mediaDevices',
  restrictions: {
    getUserMedia() {
      throw new Error('请使用app的摄像头和麦克风功能')
    },
    enumerateDevices() {
      throw new Error('请使用app的列举媒体设备功能')
    },
    getDisplayMedia() {
      throw new Error('请使用app的获取屏幕录制功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.mediaDevices) {
      Object.defineProperty(window.navigator.mediaDevices, 'getUserMedia', {
        value: this.restrictions.getUserMedia,
        writable: false,
        configurable: false,
        enumerable: true,
      })

      Object.defineProperty(window.navigator.mediaDevices, 'enumerateDevices', {
        value: this.restrictions.enumerateDevices,
        writable: false,
        configurable: false,
        enumerable: true,
      })

      Object.defineProperty(window.navigator.mediaDevices, 'getDisplayMedia', {
        value: this.restrictions.getDisplayMedia,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
