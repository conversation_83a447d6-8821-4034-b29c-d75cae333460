/**
 * 设备方向限制模块
 */
export const deviceOrientation = {
  id: 'deviceOrientation',
  name: '设备方向',
  description: '限制设备方向相关API',
  target: 'DeviceOrientationEvent',
  restrictions: {
    requestPermission() {
      throw new Error('请使用app的设备方向功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.DeviceOrientationEvent) {
      Object.defineProperty(window.DeviceOrientationEvent, 'requestPermission', {
        value: this.restrictions.requestPermission,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
