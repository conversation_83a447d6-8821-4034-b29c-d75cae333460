/**
 * 地理位置限制模块
 */
export const geolocation = {
  id: 'geolocation',
  name: '地理位置',
  description: '限制地理位置相关API',
  target: 'navigator.geolocation',
  restrictions: {
    getCurrentPosition() {
      throw new Error('请使用app的定位功能')
    },
    watchPosition() {
      throw new Error('请使用app的定位功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.geolocation) {
      Object.defineProperty(window.navigator.geolocation, 'getCurrentPosition', {
        value: this.restrictions.getCurrentPosition,
        writable: false,
        configurable: false,
        enumerable: true,
      })

      Object.defineProperty(window.navigator.geolocation, 'watchPosition', {
        value: this.restrictions.watchPosition,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
