/**
 * 存储限制模块
 */
export const storage = {
  id: 'storage',
  name: '持久化存储',
  description: '限制持久化存储相关API',
  target: 'navigator.storage',
  restrictions: {
    persist() {
      throw new Error('请使用app的持久化存储功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.storage) {
      Object.defineProperty(window.navigator.storage, 'persist', {
        value: this.restrictions.persist,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
