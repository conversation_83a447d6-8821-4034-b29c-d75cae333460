/**
 * 剪贴板限制模块
 */
export const clipboard = {
  id: 'clipboard',
  name: '剪贴板',
  description: '限制剪贴板相关API',
  target: 'navigator.clipboard',
  restrictions: {
    readText() {
      throw new Error('请使用app的读取剪贴板功能')
    },
    writeText() {
      throw new Error('请使用app的写入剪贴板功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.clipboard) {
      Object.defineProperty(window.navigator.clipboard, 'readText', {
        value: this.restrictions.readText,
        writable: false,
        configurable: false,
        enumerable: true,
      })

      Object.defineProperty(window.navigator.clipboard, 'writeText', {
        value: this.restrictions.writeText,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
