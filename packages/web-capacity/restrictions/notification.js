/**
 * 通知限制模块
 */
export const notification = {
  id: 'notification',
  name: '通知',
  description: '限制通知相关API',
  target: 'Notification',
  restrictions: {
    requestPermission() {
      throw new Error('请使用app的通知权限')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.Notification) {
      Object.defineProperty(window.Notification, 'requestPermission', {
        value: this.restrictions.requestPermission,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
