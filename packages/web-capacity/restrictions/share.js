/**
 * 分享限制模块
 */
export const share = {
  id: 'share',
  name: '分享',
  description: '限制分享相关API',
  target: 'navigator.share',
  restrictions: {
    share() {
      throw new Error('请使用app的分享功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.share) {
      Object.defineProperty(window.navigator, 'share', {
        value: this.restrictions.share,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
