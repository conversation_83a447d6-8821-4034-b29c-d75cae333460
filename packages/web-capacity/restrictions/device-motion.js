/**
 * 设备运动限制模块
 */
export const deviceMotion = {
  id: 'deviceMotion',
  name: '设备运动',
  description: '限制设备运动相关API',
  target: 'DeviceMotionEvent',
  restrictions: {
    requestPermission() {
      throw new Error('请使用app的设备运动功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.DeviceMotionEvent) {
      Object.defineProperty(window.DeviceMotionEvent, 'requestPermission', {
        value: this.restrictions.requestPermission,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
