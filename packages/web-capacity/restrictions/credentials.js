/**
 * 凭证限制模块
 */
export const credentials = {
  id: 'credentials',
  name: '凭证',
  description: '限制凭证相关API',
  target: 'navigator.credentials',
  restrictions: {
    get() {
      throw new Error('请使用app的凭证功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.credentials) {
      Object.defineProperty(window.navigator.credentials, 'get', {
        value: this.restrictions.get,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
