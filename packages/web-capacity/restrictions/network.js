/**
 * 网络状态限制模块
 */
export const network = {
  id: 'network',
  name: '网络状态',
  description: '限制网络状态查询相关API',
  target: 'navigator.connection',
  restrictions: {
    get() {
      throw new Error('请使用app的查询网络状态功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.connection) {
      Object.defineProperty(window.navigator.connection, 'get', {
        value: this.restrictions.get,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
