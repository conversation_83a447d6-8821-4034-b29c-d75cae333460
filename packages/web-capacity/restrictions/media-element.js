/**
 * 媒体元素限制模块
 */
export const mediaElement = {
  id: 'mediaElement',
  name: '媒体元素',
  description: '限制HTML媒体元素相关API',
  target: 'HTMLMediaElement',
  restrictions: {
    play() {
      throw new Error('请使用app的媒体播放功能')
    },
    requestPictureInPicture() {
      throw new Error('请使用app的画中画功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.HTMLMediaElement && window.HTMLMediaElement.prototype) {
      Object.defineProperty(window.HTMLMediaElement.prototype, 'play', {
        value: this.restrictions.play,
        writable: false,
        configurable: false,
        enumerable: true,
      })

      Object.defineProperty(window.HTMLMediaElement.prototype, 'requestPictureInPicture', {
        value: this.restrictions.requestPictureInPicture,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
