/**
 * 蓝牙限制模块
 */
export const bluetooth = {
  id: 'bluetooth',
  name: '蓝牙',
  description: '限制蓝牙相关API',
  target: 'navigator.bluetooth',
  restrictions: {
    requestDevice() {
      throw new Error('请使用app的蓝牙功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.bluetooth) {
      Object.defineProperty(window.navigator.bluetooth, 'requestDevice', {
        value: this.restrictions.requestDevice,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
