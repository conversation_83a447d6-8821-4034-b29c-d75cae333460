/**
 * 权限查询限制模块
 */
export const permissions = {
  id: 'permissions',
  name: '权限查询',
  description: '限制权限查询相关API',
  target: 'navigator.permissions',
  restrictions: {
    query() {
      throw new Error('请使用app的权限查询功能')
    },
  },

  /**
   * 应用限制
   */
  apply() {
    if (window.navigator && window.navigator.permissions) {
      Object.defineProperty(window.navigator.permissions, 'query', {
        value: this.restrictions.query,
        writable: false,
        configurable: false,
        enumerable: true,
      })
    }
  },
}
