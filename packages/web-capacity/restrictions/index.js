/**
 * 功能限制模块统一导出
 */
import { geolocation } from './geolocation.js'
import { clipboard } from './clipboard.js'
import { mediaDevices } from './media-devices.js'
import { permissions } from './permissions.js'
import { credentials } from './credentials.js'
import { bluetooth } from './bluetooth.js'
import { share } from './share.js'
import { network } from './network.js'
import { storage } from './storage.js'
import { mediaElement } from './media-element.js'
import { notification } from './notification.js'
import { deviceOrientation } from './device-orientation.js'
import { deviceMotion } from './device-motion.js'
import { print } from './print.js'

// 所有可用的限制模块
export const restrictionModules = {
  geolocation,
  clipboard,
  mediaDevices,
  permissions,
  credentials,
  bluetooth,
  share,
  network,
  storage,
  mediaElement,
  notification,
  deviceOrientation,
  deviceMotion,
  print,
}

// 模块列表（用于配置界面）
export const moduleList = Object.values(restrictionModules).map((module) => ({
  id: module.id,
  name: module.name,
  description: module.description,
  target: module.target,
}))

/**
 * 功能限制器类
 */
export class FeatureRestrictor {
  static instance = null

  constructor(enabledModules = []) {
    this.enabledModules = enabledModules
    this.appliedModules = new Set()
  }

  static getInstance(enabledModules) {
    if (!FeatureRestrictor.instance) {
      FeatureRestrictor.instance = new FeatureRestrictor(enabledModules)
    }
    return FeatureRestrictor.instance
  }

  /**
   * 初始化并应用限制
   */
  init() {
    this.enabledModules.forEach((moduleId) => {
      this.applyRestriction(moduleId)
    })
  }

  /**
   * 应用单个限制模块
   */
  applyRestriction(moduleId) {
    const module = restrictionModules[moduleId]
    if (module && !this.appliedModules.has(moduleId)) {
      try {
        module.apply()
        this.appliedModules.add(moduleId)
        console.log(`[FeatureRestrictor] 已应用限制模块: ${module.name}`)
      } catch (error) {
        console.warn(`[FeatureRestrictor] 应用限制模块失败: ${module.name}`, error)
      }
    }
  }

  /**
   * 获取已应用的模块列表
   */
  getAppliedModules() {
    return Array.from(this.appliedModules)
  }

  /**
   * 获取可用的模块列表
   */
  static getAvailableModules() {
    return moduleList
  }
}
