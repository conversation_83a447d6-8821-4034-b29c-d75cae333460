;(() => {
  const SafeMutationObserver = Object.freeze(window.MutationObserver)

  // #region 工具类
  const Utils = {
    debounce(fn, delay, immediate = false) {
      let timer = null
      return function (...args) {
        clearTimeout(timer)
        const isImmediate = immediate && !timer
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
        if (isImmediate) {
          fn.apply(this, args)
        }
      }
    },
    patchHistoryMethod(methodName, callback) {
      const original = history[methodName]
      if (!this._originalHistoryFns.has(methodName)) {
        this._originalHistoryFns.set(methodName, original)
      }

      let current = original

      Object.defineProperty(history, methodName, {
        configurable: true,
        enumerable: true,
        get() {
          return function (...args) {
            callback()
            return current.apply(this, args)
          }
        },
        set(newFn) {
          console.warn(`[patch] ${methodName} 被重写，已再次包裹`)
          current = function (...args) {
            callback()
            return newFn.apply(this, args)
          }
        },
      })
    },
    restoreHistoryPatch() {
      this._originalHistoryFns.forEach((originalFn, methodName) => {
        Object.defineProperty(history, methodName, {
          configurable: true,
          enumerable: true,
          writable: true,
          value: originalFn,
        })
      })
      this._originalHistoryFns.clear()
    },
    isHashMode() {
      return location.href.includes('#') && !location.pathname.includes('/#/')
    },
    isAndroid() {
      return /Android/i.test(navigator.userAgent)
    },
    isIOS() {
      const ua = navigator.userAgent
      const platform = navigator.platform
      const isIOSDevice = /iP(hone|od|ad)/.test(ua)
      const isIpadOS13 = platform === 'MacIntel' && navigator.maxTouchPoints > 1
      return isIOSDevice || isIpadOS13
    },
  }
  // #endregion

  // #region 资源收集器
  class ResourceCollector {
    static instance = null

    static IMGTYPE = Object.freeze({
      JPG: 'jpg',
      JPEG: 'jpeg',
      PNG: 'png',
      GIF: 'gif',
      WEBP: 'webp',
      TIFF: 'tiff',
      SVG: 'svg',
      BASE64: 'base64',
    })

    static SELECTOR_MAP = new Map([
      [
        'img',
        {
          type: 'images',
          attr: 'src',
          processFunc: (element, attr) => {
            const src = element[attr]
            const type = ResourceCollector.getImageType(src)
            if (src) {
              /* if (type === ResourceCollector.IMGTYPE.BASE64) {
                ResourceCollector.getInstance().pauseObserver()
                element[attr] = ''
                ResourceCollector.getInstance().resumeObserver()
              } */
              return {
                type,
                src: type !== ResourceCollector.IMGTYPE.BASE64 ? src : '',
              }
            }
            return null
          },
        },
      ],
      ['source[type^="image"]', { type: 'images', attr: 'srcset' }],
      ['picture source', { type: 'images', attr: 'srcset' }],
      ['video', { type: 'videos', attr: 'src', hasSource: true }],
      ['audio', { type: 'audios', attr: 'src', hasSource: true }],
      ['link[rel="stylesheet"]', { type: 'stylesheets', attr: 'href' }],
      ['script', { type: 'scripts', attr: 'src' }],
      ['iframe', { type: 'iframes', attr: 'src' }],
    ])

    constructor() {
      this.store = {}
      this.observer = null
      this.storeEntries = []
      this._originalHistoryFns = new Map()

      this.handleResourceUpdate = Utils.debounce(() => {
        this.storeEntries.forEach(([, set]) => set.clear())
        this.notifyApp(this.collect())
      }, 500)
      this.handleUrlChange = Utils.debounce(() => {
        this.handleResourceUpdate()
        this.notifyAppUrlChange(window.location.href)
      }, 500)
    }

    static getInstance() {
      if (!ResourceCollector.instance) {
        ResourceCollector.instance = new ResourceCollector()
      }
      return ResourceCollector.instance
    }

    init() {
      this.store = Object.fromEntries(
        Array.from(ResourceCollector.SELECTOR_MAP.values(), ({ type }) => [type, new Set()]),
      )
      this.storeEntries = Object.entries(this.store)
      this.startWatching()
    }

    static getImageType(src) {
      try {
        if (!src || typeof src !== 'string') return 'unknown'
        if (src.startsWith('data:image/')) return this.IMGTYPE.BASE64
        const urlWithoutParams = src.split('?')[0].split('#')[0]
        const extension = urlWithoutParams.split('.').pop().toLowerCase()
        return Object.values(this.IMGTYPE).includes(extension) ? extension : 'unknown'
      } catch {
        return 'unknown'
      }
    }

    collectBySelector(selector, config) {
      const elements = document.querySelectorAll(selector)
      if (!elements.length) return

      const { type, attr, processFunc, hasSource } = config
      const resourceSet = this.store[type]

      for (const element of elements) {
        if (processFunc) {
          const result = processFunc(element, attr)
          if (result) {
            resourceSet.add(JSON.stringify(result))
          }
          continue
        }

        const attrValue = element[attr]
        if (attrValue) {
          if (selector === 'iframe') {
            this.pauseObserver()
            element[attr] = ''
            this.resumeObserver()
          } else {
            resourceSet.add(attrValue)
          }
        }

        if (hasSource) {
          element.querySelectorAll('source').forEach((source) => {
            const sourceSrc = source.src
            if (sourceSrc) resourceSet.add(sourceSrc)
          })
        }
      }
    }

    collect() {
      ResourceCollector.SELECTOR_MAP.forEach((config, selector) =>
        this.collectBySelector(selector, config),
      )
      const data = Object.fromEntries(
        this.storeEntries.map(([key, set]) => [
          key,
          Array.from(set, (item) => {
            try {
              return JSON.parse(item)
            } catch {
              return item
            }
          }),
        ]),
      )
      delete data.iframes
      return Object.freeze(data)
    }

    startWatching() {
      if (this.observer) {
        this.cleanup()
      }

      this.notifyApp(this.collect())
      // ios hash模式的url时，startWatching时通知一次，非hash不通知，安卓不管什么模式都通知
      if (Utils.isAndroid()) {
        this.notifyAppUrlChange(window.location.href)
      } else if (Utils.isIOS() && Utils.isHashMode()) {
        this.notifyAppUrlChange(window.location.href)
      }

      this.observer = this.createResourceWatcher()

      if (this.observer) {
        this.observer.observe(document.documentElement, {
          subtree: true,
          childList: true,
          attributes: true,
          attributeFilter: ['src', 'href'],
        })
      }

      this.routePatch()
      window.addEventListener('unload', this.cleanup, { once: true })
    }

    routePatch() {
      if (history.__patched) return
      history.__patched = true
      const handleUrlChange = this.handleUrlChange.bind(this)
      const patchHistoryMethod = Utils.patchHistoryMethod.bind(this)
      patchHistoryMethod('pushState', handleUrlChange)
      patchHistoryMethod('replaceState', handleUrlChange)
      patchHistoryMethod('back', handleUrlChange)
      patchHistoryMethod('forward', handleUrlChange)
      patchHistoryMethod('go', handleUrlChange)
    }

    notifyApp(urls) {
      if (!urls || typeof urls !== 'object') return
      const data = JSON.stringify(urls)
      try {
        window?.webkit?.messageHandlers?.resourceBridge?.postMessage(data)
        window?.resourceBridge?.resourceUpdate(data)
      } catch (error) {
        console.warn('发送资源列表到 App 失败:', error)
      }
    }

    notifyAppUrlChange(url) {
      if (!url || typeof url !== 'string') return
      try {
        window?.webkit?.messageHandlers?.urlChangeBridge?.postMessage(url)
        window?.urlChangeBridge?.urlUpdate(url)
      } catch (error) {
        console.warn('发送资源列表到 App 失败:', error)
      }
    }

    createResourceWatcher() {
      if (typeof SafeMutationObserver === 'function') {
        return new SafeMutationObserver(this.handleResourceUpdate.bind(this))
      }
      return null
    }

    cleanup() {
      if (this.observer) {
        this.observer.disconnect()
        this.observer = null
      }
      Utils.restoreHistoryPatch()
      // window.removeEventListener('hashchange', this.handleUrlChange)
      Object.keys(this.store).forEach((key) => this.store[key].clear())
    }

    pauseObserver() {
      if (this.observer) {
        this.observer.disconnect()
      }
    }

    resumeObserver() {
      if (this.observer) {
        this.observer.observe(document.documentElement, {
          subtree: true,
          childList: true,
          attributes: true,
          attributeFilter: ['src', 'href'],
        })
      }
    }
  }
  // #endregion

  // #region 功能限制器
  class FeatureRestrictor {
    static instance = null

    static FEATURES = {
      window: {
        navigator: {
          geolocation: {
            getCurrentPosition() {
              throw new Error('请使用app的定位功能')
            },
            watchPosition() {
              throw new Error('请使用app的定位功能')
            },
          },
          clipboard: {
            readText() {
              throw new Error('请使用app的读取剪贴板功能')
            },
            writeText() {
              throw new Error('请使用app的写入剪贴板功能')
            },
          },
          mediaDevices: {
            getUserMedia: function () {
              throw new Error('请使用app的摄像头和麦克风功能')
            },
            enumerateDevices: function () {
              throw new Error('请使用app的列举媒体设备功能')
            },
            getDisplayMedia: function () {
              throw new Error('请使用app的获取屏幕录制功能')
            },
          },
          permissions: {
            query() {
              throw new Error('请使用app的权限查询功能')
            },
          },
          credentials: {
            get() {
              throw new Error('请使用app的凭证功能')
            },
          },
          bluetooth: {
            requestDevice: function () {
              throw new Error('请使用app的蓝牙功能')
            },
          },
          share: function () {
            throw new Error('请使用app的分享功能')
          },
          connection: {
            get() {
              throw new Error('请使用app的查询网络状态功能')
            },
          },
          storage: {
            persist: function () {
              throw new Error('请使用app的持久化存储功能')
            },
          },
        },
        /* HTMLInputElement: {
          click: function (originalClick) {
            return function () {
              if (this.type === 'file') {
                throw new Error('请使用app的文件上传功能')
              }
              return originalClick.call(this)
            }
          },
        }, */
        HTMLMediaElement: {
          play: function () {
            throw new Error('请使用app的媒体播放功能')
          },
          requestPictureInPicture: function () {
            throw new Error('请使用app的画中画功能')
          },
        },
        Notification: {
          requestPermission: function () {
            throw new Error('请使用app的通知权限')
          },
        },
        DeviceOrientationEvent: {
          requestPermission: function () {
            throw new Error('请使用app的设备方向功能')
          },
        },
        DeviceMotionEvent: {
          requestPermission: function () {
            throw new Error('请使用app的设备运动功能')
          },
        },
        print: function () {
          throw new Error('请使用app的打印功能')
        },
      },
    }

    constructor() {
      this.features = FeatureRestrictor.FEATURES
    }

    static getInstance() {
      if (!FeatureRestrictor.instance) {
        FeatureRestrictor.instance = new FeatureRestrictor()
      }
      return FeatureRestrictor.instance
    }

    init() {
      this.initWithDefineProperty()
      // this.handleDOMFileInputs()
      // this.observeFileInputs()
    }

    handleDOMFileInputs() {
      const handleFileInput = (input) => {
        // 使用 Object.defineProperty 来劫持 click 事件
        try {
          Object.defineProperty(input, 'onclick', {
            configurable: false,
            get() {
              return function (e) {
                e.preventDefault()
                e.stopPropagation()
                throw new Error('请使用app的文件上传功能')
              }
            },
            set() {}, // 防止被重新赋值
          })
        } catch {}

        // 添加事件监听器
        input.addEventListener(
          'click',
          (e) => {
            e.preventDefault()
            e.stopPropagation()
            throw new Error('请使用app的文件上传功能')
          },
          true,
        )
      }

      document.querySelectorAll('input[type="file"]').forEach(handleFileInput)
    }

    observeFileInputs() {
      // 创建观察器监听新增的文件输入框
      const observer = new SafeMutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.addedNodes.length) {
            mutation.addedNodes.forEach((node) => {
              // 检查新增节点
              if (node.nodeName === 'INPUT' && node.type === 'file') {
                this.handleDOMFileInputs()
              }
              // 检查新增节点的子节点
              if (node.querySelectorAll) {
                node.querySelectorAll('input[type="file"]').forEach(() => {
                  this.handleDOMFileInputs()
                })
              }
            })
          }
        }
      })

      // 开始观察
      observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
      })
    }

    initWithDefineProperty() {
      /* HTMLInputElement.prototype.click = this.features.window.HTMLInputElement.click(
        HTMLInputElement.prototype.click,
      ) */

      // 递归处理 features 对象
      const processFeatures = (obj, target) => {
        for (const [key, value] of Object.entries(obj)) {
          if (
            typeof value === 'object' &&
            value !== null &&
            !('get' in value) &&
            typeof value !== 'function' &&
            !value.get
          ) {
            // 如果是嵌套对象，继续递归
            if (!target[key]) {
              target[key] = {}
            }
            processFeatures(value, target[key])
          } else {
            // 为属性定义不可重写的描述符
            Object.defineProperty(target, key, {
              ...(value.get
                ? {
                    get: value.get,
                    configurable: false,
                    enumerable: true,
                  }
                : {
                    value: typeof value === 'function' ? value : value,
                    writable: false,
                    configurable: false,
                    enumerable: true,
                  }),
            })
          }
        }
      }

      // 从 window 开始处理
      processFeatures(this.features.window, window)
    }
  }
  // #endregion

  // #region 初始化
  const initialize = () => {
    const featureRestrictor = FeatureRestrictor.getInstance()
    const resourceCollector = ResourceCollector.getInstance()

    featureRestrictor.init()

    Object.defineProperties(window, {
      _getAllResourceUrls: {
        value: () => resourceCollector.init(),
        writable: false,
        configurable: false,
      },
      _cleanupWatch: {
        value: () => resourceCollector.cleanup(),
        writable: false,
        configurable: false,
      },
    })
  }
  // #endregion

  initialize()
})()
