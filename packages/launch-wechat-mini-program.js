/**
 * h5 启动微信小程序
 * @param {string} appId 小程序appId
 * @param {string} path 必须是已经发布的小程序存在的页面，不可携带 query
 * @param {string} query 最大512个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%`，需要url_encode
 * @param {string} envVersion 要打开的小程序版本, release / trial / develop
 * @param {string} serverUrl 服务端加密接口地址
 */

;(function (global) {
  // 创建构造函数
  var LaunchWechatMiniProgram = function (options) {
    if (!(this instanceof LaunchWechatMiniProgram)) {
      return new LaunchWechatMiniProgram(options)
    }

    var privateData = {
      appId: options.appId,
      path: options.path,
      query: options.query,
      envVersion: options.envVersion,
      serverUrl: options.serverUrl,
    }

    Object.defineProperties(this, {
      appId: {
        get: function () {
          return privateData.appId
        },
        configurable: false,
      },
      path: {
        get: function () {
          return privateData.path
        },
        configurable: false,
      },
      query: {
        get: function () {
          return privateData.query
        },
        configurable: false,
      },
      envVersion: {
        get: function () {
          return privateData.envVersion
        },
        configurable: false,
      },
      serverUrl: {
        get: function () {
          return privateData.serverUrl
        },
        configurable: false,
      },
    })
  }

  // 明文 URL Scheme
  LaunchWechatMiniProgram.prototype.getDecryptedUrlScheme = function () {
    return (
      'weixin://dl/business/?appid=' +
      this.appId +
      '&path=' +
      this.path +
      '&query=' +
      encodeURIComponent(this.query) +
      '&env_version=' +
      this.envVersion
    )
  }

  // 向服务端请求加密后的 URL Scheme
  LaunchWechatMiniProgram.prototype.getEncryptedUrlScheme = function (callback, errorCallback) {
    if (!this.serverUrl) {
      throw new Error('serverUrl is required')
    }
    var params = {
      appId: this.appId,
      path: this.path,
      query: this.query,
      env_version: this.envVersion,
    }

    var xhr = new XMLHttpRequest()
    xhr.open('POST', this.serverUrl, true)
    xhr.setRequestHeader('Content-Type', 'application/json')

    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            var response = JSON.parse(xhr.responseText)
            if (response && response.data) {
              if (typeof callback === 'function') {
                callback(response.data)
              }
            } else {
              if (typeof errorCallback === 'function') {
                errorCallback(new Error('Invalid response'))
              }
            }
          } catch (e) {
            if (typeof errorCallback === 'function') {
              errorCallback(e)
            }
          }
        } else {
          if (typeof errorCallback === 'function') {
            errorCallback(new Error('Request failed: ' + xhr.status))
          }
        }
      }
    }

    xhr.send(JSON.stringify(params))
  }

  // 防止原型被修改
  Object.freeze(LaunchWechatMiniProgram.prototype)

  // 防止构造函数被修改
  Object.defineProperty(global, 'LaunchWechatMiniProgram', {
    value: LaunchWechatMiniProgram,
    writable: false,
    configurable: false,
  })

  // 导出模块
  if (typeof module !== 'undefined' && module.exports) {
    Object.defineProperty(module, 'exports', {
      value: LaunchWechatMiniProgram,
      writable: false,
      configurable: false,
    })
  }
})(typeof window !== 'undefined' ? window : this)
