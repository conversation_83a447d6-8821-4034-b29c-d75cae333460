'use strict'
;(function (global, factory) {
  // AMD和CMD模块加载方式
  if (typeof define === 'function' && (define.amd || define.cmd)) {
    define(function () {
      return factory(global)
    })
  } else {
    // 全局变量
    factory(global, true)
  }
})(window, function (window, shouldUseGlobal) {
  // 私有属性/方法
  var privateStore = {
    isDebug: false, // 是否开启调试模式
    listeners: {}, // 添加事件监听器存储

    // 事件处理方法
    $on: function (eventName, callback) {
      if (typeof callback !== 'function') {
        throw new Error('Callback must be a function')
      }
      if (!this.listeners[eventName]) {
        this.listeners[eventName] = []
      }
      this.listeners[eventName].push(callback)
    },
    $remove: function (eventName, callback) {
      var eventListeners = this.listeners[eventName]
      if (eventListeners && callback) {
        var index = eventListeners.indexOf(callback)
        if (index > -1) {
          eventListeners.splice(index, 1)
        }
        if (eventListeners.length === 0) {
          delete this.listeners[eventName]
        }
      } else if (!callback) {
        delete this.listeners[eventName]
      }
    },

    $emit: function (eventName) {
      var args = Array.prototype.slice.call(arguments, 1)
      var eventListeners = this.listeners[eventName]
      if (eventListeners) {
        for (var i = 0; i < eventListeners.length; i++) {
          eventListeners[i].apply(null, args)
        }
      }
    },

    // 是否为对象
    isPlainObject: function (value) {
      if (!value || Object.prototype.toString.call(value) !== '[object Object]') {
        return false
      }
      return Object.getPrototypeOf(value) === Object.prototype
    },

    // 验证参数是否为对象
    validateParamsIsObject: function (data) {
      if (data && !this.isPlainObject(data)) {
        throw new Error('Event data must be an object')
      }
    },

    // 解析数据
    parseResponse: function (res) {
      try {
        return typeof res === 'string' ? JSON.parse(res) : res
      } catch (error) {
        throw new Error(error.message)
      }
    },

    // 平台判断
    getPlatform: function () {
      var ua = navigator.userAgent.toLowerCase()
      if (/iphone|ipad|ipod|ios/.test(ua)) {
        return 'ios'
      }
      if (/android/.test(ua)) {
        return 'android'
      }
      if (/openharmony/.test(ua)) {
        return 'openHarmony'
      }
      return 'unknown'
    },

    debugLog: function () {
      if (this.isDebug) {
        var args = Array.prototype.slice.call(arguments).map(function (arg) {
          if (typeof arg === 'object' && arg !== null) {
            return JSON.stringify(arg)
          }
          return arg
        })
        console.log.apply(console, ['[debugLog]'].concat(args))
      }
    },

    postMessage: function (params) {
      this.debugLog('Sending message:', params)

      var data = JSON.stringify(params)

      // 平台判断, 发送消息
      var platform = this.getPlatform()

      switch (platform) {
        case 'ios':
          if (
            window.webkit &&
            window.webkit.messageHandlers &&
            window.webkit.messageHandlers.nativeInvoke
          ) {
            window.webkit.messageHandlers.nativeInvoke.postMessage(data)
          }
          break
        case 'android':
          if (window.nativeInvoke) {
            window.nativeInvoke.postMessage(data)
          }
          break
        case 'openHarmony':
          if (window.nativeInvoke) {
            window.nativeInvoke.postMessage(data)
          }
          break
      }
    },

    postAndListen: function (eventName, options) {
      // 参数校验
      if (options) {
        privateStore.validateParamsIsObject(options)
      } else {
        options = {}
      }

      // 默认值
      var isComplete = false
      var defaultData = {
        success: function () {},
        fail: function () {},
        complete: function () {},
      }
      var funcKey = ['success', 'fail', 'complete']

      for (var key in defaultData) {
        if (!(key in options)) {
          options[key] = defaultData[key]
        } else {
          if (funcKey.includes(key) && typeof options[key] !== 'function') {
            throw new Error('Callback must be a function')
          }
        }
      }

      // 清理函数
      function cleanup() {
        privateStore.$remove(eventName)
        privateStore.$remove(eventName + '_fail')
      }

      function handleSuccess(result) {
        if (!isComplete) {
          isComplete = true
          cleanup()
          options.success(result)
          options.complete({ code: result.code })
        }
      }

      function handleError(error) {
        if (!isComplete) {
          isComplete = true
          cleanup()
          options.fail(error)
          options.complete({ code: error.code })
        }
      }

      // 监听成功和失败事件
      privateStore.$on(eventName, handleSuccess)
      privateStore.$on(eventName + '_fail', handleError)

      // 发送消息，过滤掉 function 类型
      var data = JSON.parse(JSON.stringify(options))
      this.postMessage({
        type: eventName,
        data: data,
      })
    },
  }

  // Fx构造函数
  function Fx() {
    /* this.init = function (options) {
      privateStore.postAndListen('init', options)
    } */
  }

  // App回调处理
  Fx.prototype.callBackToH5 = function (res) {
    try {
      privateStore.debugLog('Received response:', res)
      var parsedRes = privateStore.parseResponse(res)
      var type = parsedRes.type
      var code = parsedRes.code
      var data = parsedRes.data
      var msg = parsedRes.msg
      var bizCode = parsedRes.biz_code

      // 参数校验
      if (!parsedRes || !type) {
        throw new Error('Invalid response')
      }

      // 事件名
      var eventName = code !== 0 ? type + '_fail' : type
      // 清除type
      delete parsedRes.type
      var result = {
        code: code,
        msg: msg,
        bizCode: bizCode,
        data: data,
      }
      privateStore.$emit(eventName, result)
    } catch (error) {
      console.error('Error in callBackToH5:', error)
      privateStore.debugLog('Error in callBackToH5:', error)
    }
  }
  // 创建Fx实例
  var instance = new Fx()

  if (shouldUseGlobal) {
    // 将实例挂载到window上
    Object.defineProperty(window, 'fx', {
      value: instance,
      writable: false,
      configurable: false,
      enumerable: false,
    })
  } else {
    return instance
  }

  document.dispatchEvent(new CustomEvent('fxLifeJSReady'))
})
